const sinon = require('sinon');

const mailchimp = require('@mailchimp/mailchimp_marketing');
const admin = require('firebase-admin');
const aws = require('aws-sdk');
const iap = require('in-app-purchase');
const cfsign = require('aws-cloudfront-sign');
const admobSSV = require('admob-rewarded-ads-ssv');
const mailchimpTransactional = require('../lib/mailchimp-transactional');
const basic = require('../lib/basic');
const constants = require('../lib/constants');
const { mockUserDetails } = require('./utilities/mockuser');
const twilioClient = require('../lib/twilio-config');
const { validatedNumber } = require('../lib/phone-numbers');
const curExLib = require('../lib/currency-exchange');
const googleReverseImage = require('../lib/google-reverse-image');
const googleTranslate = require('../lib/google-translate');
const hive = require('../lib/hive');
const stripe = require('../lib/stripe')
const metricsLib = require('../lib/metrics');
const openai = require('../lib/openai');
const {google} = require("googleapis");
const pusher = require('../lib/pusher');
const openaiClient = require('../lib/openai-client');
const claudeClient = require('../lib/claude-client');
const togetherClient = require('../lib/together-client');
const promptHelper = require('../lib/prompt-helper');
const bunny = require('../lib/bunny');
const azureFace = require('../lib/azure-face');
const axios = require("axios");
const { conversionEmitter } = require('../lib/conversion');
const HiveService = require('../lib/image-moderation/hive');
const deepgram = require('@deepgram/sdk');
const { RequestBuilder } = require('yoti');
const replicateClient = require('../lib/replicate-client');
const appStoreConnectLib = require('../lib/app-store-connect');
const { v4: uuidv4 } = require('uuid');
const sqs_push = require('../lib/ai-image/sqs-push')
const detectLanguage = require('../lib/detect-language')

const FIREBASE_ERROR_CODES = ['messaging/invalid-registration-token', 'messaging/registration-token-not-registered'];

const IAP_ERROR_RECEIPTS = {
  HACK_GOOGLE: 'hacked_google',
  ELSEWHERE_GOOGLE: 'elsewhere_google',
  HACK_APPLE: 'hacked_apple',
};
class MockFirebaseError extends Error {
  constructor(code) {
    super('Mock Firebase Error');
    this.code = code;
  }
}

const FAKE_Translate_return = "this is a fake translateToEnglish return"

function getFirebaseError(token) {
  if (token.includes('invalid_token')) {
    return new MockFirebaseError('messaging/invalid-registration-token');
  }
  if (token.includes('not_reg_token')) {
    return new MockFirebaseError('messaging/registration-token-not-registered');
  }
  if (token.includes('unknown_token')) {
    return new MockFirebaseError('messaging/unknown-error');
  }
  return false;
}

const notifs = {
  numSent: 0,
  recent: null,
  recentArray: [],
  recentData: null,
  recentMulticast: null,
};

/// /holds mock firebase users data

let userStore = {};

let deletedUids = [];

function getDeletedUids(){
  return deletedUids;
}

fakeGoogleAuthJwtClient = {
  authorize() {
    const impl = function (resolve, reject) {
      return resolve({
        access_token: '0',
      });
    };
    return new Promise(impl);
  },
}

/// /remember to clear the userStore everytime the fakeAdminAuth.verifyIdToken stub is used
fakeAdminAuth = {

  getUser(uid) {
    const impl = function (resolve, reject) {
      const firebaseData = userStore[uid];
      if (firebaseData) {
        return resolve({
          email: firebaseData[0],
          emailVerified: true,
          phoneNumber: firebaseData[1],
          toJSON() { return 'MOCK'; },
        });
      }
      return resolve({
        email: '<EMAIL>',
        emailVerified: true,
        toJSON() { return 'MOCK'; },
      });
    };
    return new Promise(impl);
  },

  getUserByPhoneNumber(phoneNumber) {
    const impl = function (resolve, reject) {
      const found = Object.values(userStore).find((value) => (value[1] == phoneNumber));
      if (found) {
        return resolve({phone_number: phoneNumber});
      }
      reject();
    };
    return new Promise(impl);
  },

  deleteUser(uid) {
    console.log(uid);
    deletedUids.push(uid);
    const impl = function (resolve, reject) {
      console.log('fake delete user stub called');
      return resolve();
    };
    return new Promise(impl);
  },

  verifyIdToken(idToken) {
    const impl = function (resolve, reject) {
      const mockUser = mockUserDetails(idToken);
      if (mockUser) {
        userStore[mockUser.uid] = [mockUser.email, mockUser.phoneNumber];
        idToken = mockUser.uid;
      }
      resolve({
        uid: idToken,
        phone_number: idToken == 'email' ? undefined : idToken,
        email: mockUser?.email,
      });
    };
    return new Promise(impl);
  },

  updateUser(uid, userData) {
    const impl = function (resolve, reject) {
      const { phoneNumber, email } = userData;
      if (phoneNumber !== undefined) {
        if (phoneNumber !== null && validatedNumber(phoneNumber) === null) {
          return reject(new Error('Firebase Error: Invalid Number!'));
        }
        const userRecord = userStore[uid];
        if (!userRecord) {
          return reject(new Error('Firebase Error: User does not exist'));
        }
        if (Object.keys(userStore).find((userId) => (
          (userId != uid)
          && (userStore[userId][1] === phoneNumber)
        ))) {
          return reject(new Error('Firebase Error: Phone Number already linked to another user'));
        }
        userRecord[1] = phoneNumber;
        return resolve({
          phoneNumber: userRecord[1] || undefined,
        });
      }
      if (email !== undefined) {
        if (!email.includes('@')) {
          return reject(new Error('Firebase Error: Invalid Email!'));
        }
        const userRecord = userStore[uid];
        if (!userRecord) {
          return reject(new Error('Firebase Error: User does not exist'));
        }
        if (Object.keys(userStore).find((userId) => (
          (userId != uid)
          && (userStore[userId][0] === email)
        ))) {
          return reject(new Error('Firebase Error: Email already linked to another user'));
        }
        userRecord[0] = email;
        return resolve({
          email: userRecord[0] || undefined,
        });
      }
    };
    return new Promise(impl);
  },
  generateSignInWithEmailLink(email, actionCodeSettings) {
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
};

fakeAdminMessaging = {

  send(message) {
    const impl = function (resolve, reject) {
      console.log('Fake messaging().send()', message);
      const firebaseErr = getFirebaseError(message.token);
      if (firebaseErr) {
        console.log('Fake messaging().send() error');
        return reject(firebaseErr);
      }
      notifs.numSent += 1;
      if (message.notification) {
        notifs.recent = message;
        notifs.recentArray.push(message);
      } else {
        notifs.recentData = message;
      }
      resolve({
        response: 'success',
      });
    };
    return new Promise(impl);
  },

  sendMulticast(message) {
    console.log(`Fake messaging().sendMulticast(), ${JSON.stringify(message, null, 2)}`);
    notifs.numSent += 1;
    notifs.recentMulticast = message;
    const impl = function (resolve, reject) {
      let failureCount = 0;
      let successCount = 0;
      const responses = message.tokens.map((token) => {
        const firebaseError = getFirebaseError(token);
        const success = true;
        if (firebaseError) {
          failureCount++;
          return {
            success: false,
            error: firebaseError,
          };
        }
        successCount++;
        return {
          success: true,
          messageId: 'messageId',
        };
      });
      resolve({
        failureCount,
        responses,
        successCount,
      });
    };
    return new Promise(impl);
  },

  subscribeToTopic(tokens, topic) {
    console.log(`Fake messaging().subscribeToTopic(), ${tokens}, ${topic}`);
    const impl = function (resolve, reject) {
      resolve({
        failureCount: 0,
        successCount: tokens.length,
      });
    };
    return new Promise(impl);
  },

  unsubscribeFromTopic(tokens, topic) {
    console.log(`Fake messaging().unsubscribeFromTopic(), ${tokens}, ${topic}`);
    const impl = function (resolve, reject) {
      resolve({
        failureCount: 0,
        successCount: tokens.length,
      });
    };
    return new Promise(impl);
  },
};

fakeAdminAppCheck = {
  verifyToken: sinon.stub().resolves({ app_id: 'demo-app', aud: ['my-endpoint'] }),
};


fakeS3ManagedUpload = {

  send(cb) {
    console.log('calling fake s3 managed upload send');
    cb(null, {
      Location: 'mock',
      ETag: 'mock',
      VersionId: 'mock',
      Key: 'random-s3-file-key',
    });
  },

  on(event, cb) {
  },
};

fakeS3 = {

  listObjectsV2() {
    const impl = function (resolve, reject) {
      resolve({
        Contents: [],
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  deleteObjects() {
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  deleteObject(params, cb) {
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  upload(params, cb) {
    console.log('calling fake s3 upload');
    if (cb) {
      return fakeS3ManagedUpload.send(cb);
    }
    return fakeS3ManagedUpload;
  },

  putObject(params) {
    const impl = function (resolve, reject) {
      resolve(params);
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  headObject(params) {
    const impl = function (resolve, reject) {
      resolve(params);
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  copyObject(params) {
    const impl = function (resolve, reject) {
      resolve(params);
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  getSignedUrlPromise(params) {
    const impl = function (resolve, reject) {
      resolve('MOCK_SIGNED_URL');
    };
    return new Promise(impl);
  },

};
fakeRekognition = {};
fakeLambda = {};
fakeCloudwatch = {
  putMetricData(params) {
    console.log('Fake cloudwatch putMetricData', JSON.stringify(params));
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
};
fakeMediaConvert = {
  createJob(params) {
    const impl = function (resolve, reject) {
      resolve({
        Job: {
          Id: 'id',
        },
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  getJob(params) {
    const impl = function (resolve, reject) {
      resolve({
        Job: {
          Status: 'COMPLETE',
        },
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  },
};
fakeSNS = {
  publish(params) {
    console.log('Fake SNS publish', JSON.stringify(params));
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
};
fakeSES = {
  sendTemplatedEmail(params) {
    console.log('Fake SES sendTemplatedEmail', JSON.stringify(params));
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  sendBulkTemplatedEmail(params) {
    console.log('Fake SES sendBulkTemplatedEmail', JSON.stringify(params));
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  sendEmail(params) {
    console.log('Fake SES sendEmail', JSON.stringify(params));
    const impl = function (resolve, reject) {
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
};
fakeTwilioClient = {
  messages: {
    async create(params) {
      console.log(`Triggered fake twilio sms: ${JSON.stringify(params)}`);
      return ({});
    },
  },
};
fakePusher = {
  sendToUser(userId, eventName, event) {
    console.log('Fake pusher sendToUser', userId, eventName, event);
    return;
  },
  trigger(channel, eventName, event) {
    console.log('Fake pusher trigger', channel, eventName, event);
    return;
  },
  authenticateUser(socketId, user) {
    console.log('Fake pusher authenticateUser', socketId, user);
    return {
      auth: user.id,
      user_data: user.id,
    };
  },
  authorizeChannel(socketId, channel) {
    console.log('Fake pusher authorizeChannel', socketId, channel);
    return {
      auth: socketId,
    };
  },
}

fakeStripe = {};
fakeStripeCheckoutSessionsCreateParams = null;

fakeOpenaiClient = {};
fakeClaudeClient = {};


setMockPromptResponse = function setMockPromptResponse(mockResponse) {
  fakeOpenaiClient.chat = {
    completions: {
      async create(params) {
        console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
        return {
          usage: {
            prompt_tokens: 10,
            completion_tokens: 10,
          },
          choices: [{
            message: {
              content: mockResponse
            },
          }],
        }
      }
    }
  }
  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      return {
        usage: {
          input_tokens: 10,
          output_tokens: 10,
        },
        content: [{
          text: mockResponse
        }],
      }
    }
  }
}

setMockPromptError = function setMockPromptError(errorMessage) {
  fakeOpenaiClient.chat = {
    completions: {
      async create(params) {
        console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
        throw new Error(errorMessage);
      }
    }
  }
  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      throw new Error(errorMessage);
    }
  }
}

fakeAzureFaceCreateLivenessSession = async function () {
  const impl = function (resolve, reject) {
    resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
  };
  return new Promise(impl);
}();

fakeAzureFaceGetLivenessResult = async function () {
  const impl = function (resolve, reject) {
    resolve({
      status: 'success', data: {
        "status": "ResultAvailable",
        "result": {
          "id": 1,
          "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
          "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
          "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
          "request": {
            "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
            "method": "POST",
            "contentLength": 521802,
            "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
            "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
          },
          "response": {
            "body": {
              "livenessDecision": "realface",
              "target": {
                "faceRectangle": {
                  "top": 657,
                  "left": 162,
                  "width": 786,
                  "height": 813
                },
                "fileName": "video.webp",
                "timeOffsetWithinFile": 0,
                "imageType": "Color"
              },
              "modelVersionUsed": "2022-10-15-preview.04"
            },
            "statusCode": 200,
            "latencyInMilliseconds": 898
          },          "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
        },
        "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
        "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
        "authTokenTimeToLiveInSeconds": 600,
        "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
        "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
        "sessionExpired": true
      }
    });
  };
  return new Promise(impl);
}();

let axiosRequestStub;
let fakeHiveService = {};
let fakeRekognitionService = {};
let fakeYotiService = {};

// Function to set the mock response for both services
const setMockImageModerationResponse = (response, hiveResponse) => {
  fakeRekognitionService.moderatePicture = (params) => {
    const impl = (resolve) => resolve(response);
    return {
      promise: () => new Promise(impl),
    };
  };

  fakeHiveService.moderatePicture = (params) => {
    const impl = (resolve) => resolve(hiveResponse || response);
    return {
      promise: () => new Promise(impl),
    };
  };
};

// function to set moke response fake for Yoti
const setYotiMockResponseFake = (response, testError) => {
  fakeYotiService = {
    execute() {
      console.log('Fake Yoti service called');
      if (testError) {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject({ messgae: 'Enforced error', status: 400, response: { text: JSON.stringify({ error_message: 'Invalid secure session token.', error_code: 'INVALID_SECURE_SESSION' }) } });
      }
      return Promise.resolve({ parsedResponse: response, body: JSON.stringify(response) });
    },
  };
};

const setS3MockContentList = (response) => {
  fakeS3.listObjectsV2 = () => {
    console.log('calling fake s3 listObjectsV2');
    const impl = (resolve) => resolve(response);
    return {
      promise: () => new Promise(impl),
    };
  };
};

const fakeReplicateClient = {
  getPoseVerificationPrediction: (input) => {
    console.log('Fake replicate getPoseVerificationPrediction', input);
    return Promise.resolve({
      id: 'xag3atv30xrgm0cf7yca88gpaw',
    });
  },
  createPrediction: (model, input) => {
    console.log('Fake replicate createPrediction', model, input);
    return Promise.resolve({
      id: 'xag3atv30xrgm0cf7yca88gpaw',
    });
  },
  getPrediction: (predictionId) => {
    console.log('Fake replicate getPrediction', predictionId);
    return Promise.resolve({
      output: ['Verify'],
    });
  },
  createClipPrediction: (data) => {
    console.log('Fake replicate createClipPrediction', data);
    return Promise.resolve({
      id: 'xag3atv30xrgm0cf7yca88gpaw',
    });
  },
};

const setReplicateMockResponse = response => {
  const output = {
    metrics: {
      input_token_count: 10,
      output_token_count: 10,
    },
    output: ['Here', ' are', ' four', ' topical', ' ice', 'break', 'ers', ' tailored', ' to', ' your', ' match', "'s", ' interests', ':\n\n', '{', ' output', ':', ' ["', 'So', ',', ' what', "'s", ' the', ' most', ' spontaneous', ' thing', ' you', "'ve", ' ever', ' done', ' on', ' a', ' whim', '?",', ' "', 'What', "'s", ' the', ' best', ' book', ' you', "'ve", ' read', ' recently', '?', ' Why', ' did', ' it', ' stand', ' out', ' to', ' you', '?",', ' "', 'I', ' saw', ' you', "'re", ' into', ' hiking', '!', ' What', "'s", ' your', ' favorite', ' trail', ' you', "'ve", ' explored', ' so', ' far', '?",', ' "', 'What', "'s", ' the', ' most', ' memorable', ' concert', ' or', ' live', ' show', ' you', "'ve", ' been', ' to', '?', '"]', ' }'],
  };
  fakeReplicateClient.getPrediction = (predictionId) => {
    console.log('Fake getPrediction with response', predictionId, response);
    return Promise.resolve(response || output);
  };
};

const setFakeEmbedMergeResponse = (mergedVector) => {
  fakeLambda.invoke = (params) => {
    console.log('Fake Lambda invoke', params);
    return {
      promise: () => Promise.resolve({
        Payload: JSON.stringify({
          statusCode: 200,
          body: JSON.stringify({ merged_vector: [mergedVector] }),
        }),
      }),
    };
  };
};

function createStubs() {
  sinon.restore();

  sinon.stub(basic, 'assignConfig').returns(false);
  sinon.stub(basic, 'assignIntConfig').returns(0);

  sinon.stub(constants, 'getKarmaTiers').returns([0, 1, 3]);
  sinon.stub(constants, 'getKarmaTierSwipeLimits').returns([4, 5, 6]);
  sinon.stub(constants, 'getKarmaTierGhostMeter').returns([10, 11, 12]);
  sinon.stub(constants, 'getInterestPageSize').returns(2);
  sinon.stub(constants, 'getDailyPostLimit').returns(0);
  sinon.stub(constants, 'getReplacedInterests').returns([]);
  sinon.stub(constants, 'getBackfillChatPerUserStateCutoffDate').returns(new Date('2025-03-18'));
  sinon.stub(constants, 'enforceVerification').returns(false);
  sinon.stub(constants, 'updateLastSeenOnEveryRequest').returns(false);
  sinon.stub(constants, 'throttleScoreUpdates').returns(false);
  sinon.stub(constants, 'runPreemptiveModeration').returns(false);
  sinon.stub(constants, 'requireManualVerificationForWeb').returns(false);
  sinon.stub(constants, 'hideUnverifiedUsers').returns(false);
  sinon.stub(constants, 'disableYoti').returns(false);

  sinon.stub(metricsLib, 'getPopularLanguages').returns(['en', 'es', 'id', 'pt', 'ru', 'de', 'fr', 'it']);

  sinon.stub(openaiClient, 'getOpenaiClient').returns(fakeOpenaiClient);
  sinon.stub(openaiClient, 'getOpenaiClientForPoseVerification').returns(fakeOpenaiClient);
  sinon.stub(openaiClient, 'getFineTunedOpenaiClient').returns(fakeOpenaiClient);
  sinon.stub(claudeClient, 'getClaudeApiClient').returns(fakeClaudeClient);
  sinon.stub(togetherClient, 'getTogetherAiClient').returns(fakeOpenaiClient);
  axiosRequestStub = sinon.stub(axios, 'request').resolves({ data: 'axios stub' });

  if (!process.env.PINTEREST_EMIT) sinon.stub(conversionEmitter, 'emit');


  fakeOpenaiClient.chat = {
    completions: {
      async create(params) {
        console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
        return {
          usage: {
            prompt_tokens: 10,
            completion_tokens: 10,
          },
          choices: [{
            message: {
              content: 'mock response'
            },
          }],
        }
      }
    }
  }
  fakeOpenaiClient.moderations = {
    async create(params) {
      console.log(`Fake openai moderations: ${JSON.stringify(params)}`);
      return '';
    }
  }

  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      return {
        usage: {
          input_tokens: 10,
          output_tokens: 10,
        },
        content: [{
          text: 'mock response'
        }],
      }
    }
  }

  sinon.stub(promptHelper, 'getBase64FromImageUrl').returns({ mimeType: 'jpg', data: '' });

  sinon.stub(openai, 'isInterestAppropriate')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(false);
      };
      return new Promise(impl);
    });
  sinon.stub(openai, 'isInappropriate')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(false);
      };
      return new Promise(impl);
    });

  sinon.stub(hive, 'moderateText')
    .callsFake((params) => {
      console.log('Fake moderateText', JSON.stringify(params));
      const impl = function (resolve, reject) {
        resolve({
          body: {
            status: [
              {
                response: {
                  output: [
                    {
                      classes: [
                      ],
                    },
                  ],
                },
              },
            ],
          },
        });
      };
      return new Promise(impl);
    });

  sinon.stub(hive, 'moderateVideo')
    .callsFake((params) => {
      console.log('Fake moderateVideo', JSON.stringify(params));
      const impl = function (resolve, reject) {
        resolve({
          body: {
            status: [
              {
                response: {
                  output: [
                    {
                      classes: [
                      ],
                    },
                  ],
                },
              },
            ],
          },
        });
      };
      return new Promise(impl);
    });

  sinon.stub(hive, 'moderateOcr')
    .callsFake((params) => {
      console.log('Fake moderateOcr', JSON.stringify(params));
      const impl = function (resolve, reject) {
        resolve({
          body: {
            status: [
              {
                response: {
                  output: [
                    {
                      frame_results: [
                        {
                          block_text: 'mock',
                          classes: [
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        });
      };
      return new Promise(impl);
    });

  sinon.stub(mailchimp.lists, 'setListMember')
    .callsFake((listId, email, subscriber) => {
      console.log('Fake setListMember', listId, email, JSON.stringify(subscriber));
      if (subscriber.email_address == 'throw') {
        throw 'Mailchimp Error';
      }
      return {
        id: subscriber.email,
      };
    });

  stub = sinon.stub(mailchimpTransactional.messages, 'sendTemplate')
    .callsFake((params) => {
      console.log('Fake sendTemplate', JSON.stringify(params));
      const impl = function (resolve, reject) {
        resolve({});
      };
      return new Promise(impl);
    });

  sinon.stub(admin, 'initializeApp')
    .callsFake((param) => {

    });
  sinon.stub(admin, 'auth')
    .get(() => () => fakeAdminAuth);
  sinon.stub(admin, 'messaging')
    .get(() => () => fakeAdminMessaging);
  sinon.stub(admin, 'appCheck')
    .get(() => () => fakeAdminAppCheck);

  sinon.stub(aws, 'S3')
    .callsFake(() => fakeS3);
  sinon.stub(aws, 'Rekognition')
    .callsFake(() => fakeRekognition);
  sinon.stub(aws, 'Lambda')
    .callsFake(() => fakeLambda);
  sinon.stub(aws, 'CloudWatch')
    .callsFake(() => fakeCloudwatch);
  sinon.stub(aws, 'MediaConvert')
    .callsFake(() => fakeMediaConvert);
  sinon.stub(aws, 'SNS')
    .callsFake(() => fakeSNS);
  sinon.stub(aws, 'SES')
    .callsFake(() => fakeSES);

  sinon.stub(azureFace, 'createLivenessSession')
    .callsFake(() => fakeAzureFaceCreateLivenessSession);
  sinon.stub(azureFace, 'getLivenessResult')
    .callsFake(() => fakeAzureFaceGetLivenessResult);

  fakeRekognition.detectModerationLabels = function(params) {
    const impl = function (resolve, reject) {
      resolve({ ModerationLabels: [] });
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  fakeRekognition.detectFaces = function(params) {
    const impl = function (resolve, reject) {
      console.log('Fake Rekognition : detectFaces')
      resolve({ FaceDetails: [] });
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  fakeRekognition.indexFaces = function(params) {
    const impl = function (resolve, reject) {
      console.log('Fake Rekognition : indexFaces')
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  fakeRekognition.searchFacesByImage = function(params) {
    const impl = function (resolve, reject) {
      console.log('Fake Rekognition : searchFacesByImage')
      resolve({ FaceMatches: [] });
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  fakeRekognition.compareFaces = function(params) {
    const impl = function (resolve, reject) {
      console.log('Fake Rekognition : compareFaces')
      resolve({ FaceMatches: [ {} ] });
    };
    return {
      promise: () => new Promise(impl),
    };
  },
  fakeRekognition.deleteFaces = function(params) {
    const impl = function (resolve, reject) {
      console.log('Fake Rekognition : deleteFaces')
      resolve({});
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  fakeLambda.invoke = function(params) {
    const impl = function (resolve, reject) {
      resolve({ Payload: '{}' });
    };
    return {
      promise: () => new Promise(impl),
    };
  },

  sinon.stub(iap, 'config')
    .callsFake((param) => {

    });
  sinon.stub(iap, 'setup')
    .callsFake(() => {
      const impl = function (resolve, reject) {
        resolve({});
      };
      return new Promise(impl);
    });
  sinon.stub(iap, 'validate')
    .callsFake((receipt, cb) => {
      if (receipt === IAP_ERROR_RECEIPTS.HACK_GOOGLE) {
        cb(new Error(`Status:${400}`), { status: 1 });
      } else if (receipt === IAP_ERROR_RECEIPTS.ELSEWHERE_GOOGLE) {
        cb(new Error(`Status:${403}`), { status: 1 });
      } else if (receipt === IAP_ERROR_RECEIPTS.HACK_APPLE) {
        cb(new Error('failed to validate'), { status: 2 });
      } else {
        cb(null, receipt);
      }
    });

  sinon.stub(cfsign, 'getSignedUrl')
    .callsFake((url, options) => `${url}?signature=mocked`);

  sinon.stub(twilioClient, 'getClient')
    .callsFake(() => fakeTwilioClient);

  sinon.stub(google.auth, 'JWT')
    .callsFake(() => fakeGoogleAuthJwtClient);

  sinon.stub(admobSSV, 'verify')
    .callsFake(() => {
      const impl = function (resolve, reject) {
        resolve({});
      };
      return new Promise(impl);
    });

  sinon.stub(curExLib, 'getExchangeData')
    .callsFake(() => ({
      ABC: 1.5,
      UFC: 1,
      JPY: 0.01,
      JOD: 2,
    }));

  sinon.stub(googleTranslate, 'detect').callsFake(async (params) => {
    console.log('fake detectLanguage called', JSON.stringify(params));
    let language = 'en'
    if (['und', 'de', 'zh-CN', 'da', 'id','ja'].includes(params)) {
      language = params;
    }
    else if(params === 'expect error'){
      language = 'hi-Latn'
    }else if(params.slice(0, 1) === '#') {
      // Take the first two words
      language = params.slice(1, 3); // Takes characters at index 0 and 1
    }
    return [ {
      confidence: 1,
      language,
      input: params,
    } ];
  });

  sinon.stub(googleTranslate, 'translateToEnglish').callsFake(async (params) => {
    console.log('fake translateToEnglish called', JSON.stringify(params));
    if (params === 'expect error') {
      throw new Error('Stub, failed to translate')
    }else{
      return FAKE_Translate_return
    }

  });

  sinon.stub(bunny, 'purge').callsFake(async (params) => {
    console.log('fake bunny purge called', JSON.stringify(params));
  });

  sinon.stub(googleReverseImage, 'imageDetection').callsFake(async (params) => {
    console.log('fake imageDetection called', JSON.stringify(params));
    return {
      webDetection: {
        fullMatchingImages: [],
        partialMatchingImages: [],
      },
      error: null,
    };
  });

  fakeStripe = {
    deletedCustomerIds: [],

    customers: {
      async create(params) {
        console.log(`Fake stripe customers create: ${JSON.stringify(params)}`);
        return { id: 'fake' };
      },
      async del(params) {
        console.log(`Fake stripe customers del: ${JSON.stringify(params)}`);
        fakeStripe.deletedCustomerIds.push(params);
        return { deleted: true };
      },
    },
    prices: {
      async list(params) {
        console.log(`Fake stripe prices list: ${JSON.stringify(params)}`);
        return { data: [ { id: params.lookup_keys[0] } ] };
      },
    },
    paymentIntents: {
      async list(params) {
        console.log(`Fake stripe paymentIntents list: ${JSON.stringify(params)}`);
        if (params.customer == 'test_paymentIntents_currency_backfill') {
          return { data: [
            { status: 'failed' },
            { status: 'succeeded', currency: 'jpy', amount: 1000 },
          ] };
        }
        return { data: [ ] };
      },
    },
    checkout: {
      sessions: {
        async create(params) {
          console.log(`Fake stripe sessions create: ${JSON.stringify(params)}`);
          fakeStripeCheckoutSessionsCreateParams = params;
          let url = params.line_items[0].price + (params.discounts ? ('-' + params.discounts[0].coupon) : '') + '-' + params.mode;
          if (params.success_url && params.success_url != 'https://boo.world') {
            url = params.success_url;
          }
          return { url };
        },
        async retrieve(session_id, params) {
          console.log(`Fake stripe sessions retrieve: ${JSON.stringify(params)}`);
          console.log(session_id);
          return { line_items: { data: [ { price: { lookup_key: session_id } } ] } };
        },
      }
    },
    webhooks: {
      constructEvent(data, sig, endPointSecret) {
        return data;
      }
    },
    subscriptions: {
      async list(params) {
        console.log(`Fake subscriptions : ${JSON.stringify(params)}`);
        if (params.customer === 'stripeCustomer_123'){
          return {
            data: [
              {
                id: 'sub1',
                current_period_end: 1620000000,
              },
              {
                id: 'sub2',
                current_period_end: 1625000000, // This one has the greatest current_period_end
              },
              {
                id: 'sub3',
                current_period_end: 1610000000,
              },
            ],
          };
        }else if(params.customer === 'stripeCustomer_234') return null
        else if(params.customer === 'stripeCustomer_345') throw new Error('Error fetching subscription')
        else if(params.customer === 'stripeCustomer_1'){
          return { data: [ {
            status: 'incomplete_expired',
            items: {
              data: [{ price: { lookup_key: 'infinity_m12_x0' } }]
            },
            current_period_end: Math.floor(Date.now() / 1000) - 86400 //yesterday
          } ] };
        }
        else if(params.customer === 'stripeCustomer_2'){
          return { data: [ {
            status: 'canceled',
            items: {
              data: [{ price: { lookup_key: 'infinity_m12_x0' } }]
            },
            current_period_end: Math.floor(Date.now() / 1000) + 31536000 //next year
          } ] };
        }
        else{
          return { data: [ {
            status: 'active',
            items: {
              data: [{ price: { lookup_key: 'infinity_m12_x0' } }]
            },
            current_period_end: Math.floor(Date.now() / 1000) + 31536000 //next year
          } ] };
        }
      },
    },
  };
  sinon.stub(stripe, 'stripe').value(fakeStripe);
  sinon.stub(pusher, 'pusher').value(fakePusher);

  // Importing here, because it requires AWS Rekognition Stubbed
  // eslint-disable-next-line global-require
  const RekognitionService = require('../lib/image-moderation/rekognition');

  fakeHiveService = {
    moderatePicture(params) {
      const impl = (resolve, reject) => resolve({ isFlagged: false, detectionLabels: [] });
      return {
        promise: () => new Promise(impl),
      };
    },

    moderatePictureByUrl(params) {
      if(params === 'https://image/nsfw.png'){
        const impl = (resolve, reject) => resolve(
          {
            isFlagged: true,
            detectionLabels: [
              {
                Confidence: 99.99992847442628,
                Name: "general_nsfw",
                ParentName: ""
              }]
          }
        );

        return {
          promise: () => new Promise(impl),
        };
      }else {
        const impl = (resolve, reject) => resolve({ isFlagged: false, detectionLabels: [] });

        return {
          promise: () => new Promise(impl),
        };
      }

    },
  };

  fakeRekognitionService = {
    moderatePicture(params) {
      const impl = (resolve, reject) => resolve({ isFlagged: false, detectionLabels: [] });
      return {
        promise: () => new Promise(impl),
      };
    },
  };

  sinon.stub(HiveService.prototype, 'moderatePicture').callsFake(async (key) => fakeHiveService.moderatePicture(key).promise());
  sinon.stub(HiveService.prototype, 'moderatePictureByUrl').callsFake(async (url) => fakeHiveService.moderatePictureByUrl(url).promise());
  sinon.stub(RekognitionService.prototype, 'moderatePicture').callsFake(async (key) => fakeRekognitionService.moderatePicture(key).promise());

  const transcribeStub = sinon.stub().resolves({
    result: {
      results: {
        channels: [
          {
            alternatives: [
              {
                paragraphs: {
                  transcript: "mock transcription",
                },
              },
            ],
          },
        ],
      },
    },
    error: null,
  });

  sinon.stub(deepgram, 'createClient').returns({
    listen: {
      prerecorded: {
        transcribeUrl: transcribeStub,
      },
    },
  });

  fakeYotiService = {
    execute() {
      console.log('Fake Yoti service called');
      return Promise.resolve({ parsedResponse: { prediction: 'real' }, body: JSON.stringify({ prediction: 'real' }) });
    },
  };

  sinon.stub(RequestBuilder.prototype, 'withPemFilePath').returnsThis();
  sinon.stub(RequestBuilder.prototype, 'build').callsFake(() => {
    console.log('Fake Yoti request builder called');
    return {
      execute: async (key) => fakeYotiService.execute(key),
    };
  });
  sinon.stub(replicateClient, 'getReplicateApiClient').returns(fakeReplicateClient);

  sinon.stub(appStoreConnectLib, 'getTransaction').callsFake((originalTransactionId, transactionId) => {
    console.log('Fake appStoreConnectLib.getTransaction', originalTransactionId, transactionId);
    return;
  });
  sinon.stub(sqs_push, 'sendToFeatureQueue').callsFake(() => {
    return uuidv4();
  });
}

function reset() {
  notifs.recent = null;
  notifs.recentArray = [];
  notifs.recentData = null;
  notifs.recentMulticast = null;
  notifs.numSent = 0;
  userStore = {};
  deletedUids=[];
}

function waitFor(conditionFunction) {
  const poll = (resolve) => {
    if (conditionFunction()) resolve();
    else setTimeout((_) => poll(resolve), 10);
  };

  return new Promise(poll);
}

function restoreAxiosStub() {
  if (axiosRequestStub) {
    axiosRequestStub.restore();
  }
}

function stubAuthEmailDomain(domain) {

  if (typeof fakeAdminAuth.verifyIdToken.restore === 'function') {
    fakeAdminAuth.verifyIdToken.restore();
  }
  if (typeof fakeAdminAuth.getUser.restore === 'function') {
    fakeAdminAuth.getUser.restore();
  }

  sinon.stub(fakeAdminAuth, 'verifyIdToken').callsFake(function (idToken) {
    const impl = function (resolve, reject) {
      resolve({
        uid: idToken,
        email: `${idToken}@${domain}`,
      });
    };
    return new Promise(impl);
  });
  sinon.stub(fakeAdminAuth, 'getUser').callsFake(function (uid) {
    const impl = function (resolve, reject) {
      resolve({
        email: `${uid}@${domain}`,
        emailVerified: true,
        toJSON() { return 'MOCK'; },
      });
    };
    return new Promise(impl);
  });
}

module.exports = {
  notifs,
  getDeletedUids,
  reset,
  waitFor,
  createStubs,
  fakeTwilioClient,
  fakeAdminMessaging,
  IAP_ERROR_RECEIPTS,
  setMockPromptResponse,
  setMockPromptError,
  restoreAxiosStub,
  fakeAdminAuth,
  fakeSES,
  setMockImageModerationResponse,
  setS3MockContentList,
  setYotiMockResponseFake,
  setReplicateMockResponse,
  fakeCloudwatch,
  fakeS3,
  setFakeEmbedMergeResponse,
  stubAuthEmailDomain,
  FAKE_Translate_return
};
