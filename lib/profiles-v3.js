const moment = require('moment');
const mongoose = require('mongoose');
const deepFreeze = require('deep-freeze');
const lookup = require('country-code-lookup');
const _ = require('underscore');
const User = require('../models/user');
const ExclusionList = require('../models/exclusion-list');
const TopPicksExclusionList = require('../models/top-picks-exclusion-list');
const basicLib = require('./basic')
const premiumLib = require('./premium');
const chatLib = require('./chat');
const constants = require('./constants');
const UserMetadata = require('../models/user-metadata');
const UsersWhoLiked = require('../models/users-who-liked');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');
const { languageCodes } = require('../lib/languages');
const { ethnicities } = require('../lib/ethnicities');
const { readPreference, replicaTags } = require('../lib/read-preference-analytics');
const { moreAboutUserChoices, } = require('./moreAboutUser');
const genderPreferenceLib = require('../lib/gender-preference');
const locationLib = require('../lib/location');
const actionLib = require('../lib/action');
const projections = require('../lib/projections');
const geoip = require('geoip-lite');
const personalityLib = require('../lib/personality');
const { executeAggregationWithRetry } = require('../lib/retry-aggregate-query');
const AiFilter = require('../models/ai-filters');
const UserEmbeddings = require('../models/user-embeddings');
const turf = require("@turf/turf");
const countryLib = require('../lib/country');
const { DAILY_USER_WHO_LIKED_PROFILE_LIMIT, DAILY_USER_BOOST_USING_PROFILE_LIMIT } = require('../lib/constants');

/**
 * - profiles-v3.js is profiles-v2 from Jan-2022, but giving priority to active users throughout.
 *  - also expanding the radius for local users to 80 mi once users within 50 mi are exhausted
 * Changes:
 *  - https://gitlab.com/boo.dating/backend/-/merge_requests/18 and
 *  - https://gitlab.com/boo.dating/backend/-/merge_requests/16/diffs
 */

const calculateBoundingBox = (coordinates, radius) => {
  const center = turf.point(coordinates);
  const options = { steps: 64, units: "miles" };
  const circle = turf.circle(center, radius, options);
  return turf.bbox(circle);
};

const tier5Countries = [
  'Philippines', 'Bolivia', 'Ukraine', 'Morocco', 'Egypt', 'Laos',
  'Vietnam', 'India', 'Comoros', 'Botswana', 'Armenia', 'Georgia',
  'Democratic Republic of the Congo', 'Senegal', 'Sri Lanka', 'Nigeria',
  'Bangladesh', 'Kenya', 'Pakistan', 'Cameroon', 'Cambodia',
  'Ethiopia', 'Sudan', 'Afghanistan', 'Liberia', 'Indonesia',
  'Sierra Leone', 'Togo', 'Haiti', 'Burundi', 'Benin', 'Rwanda',
  'Chad', 'Zambia', 'Mali', 'Burkina Faso', 'Niger', 'Madagascar',
  'Nepal', 'Yemen', 'Ghana', 'Angola', 'Uganda', 'Tanzania',
].map((x) => lookup.byCountry(x).iso2);
deepFreeze(tier5Countries);

const nonTier5Countries = lookup.countries
  .filter((x) => !tier5Countries.includes(x.iso2))
  .map((x) => x.iso2);
deepFreeze(nonTier5Countries);

function isTier5(user) {
  return tier5Countries.includes(user.countryCode);
}

function formatProfiles(profiles, user, params = {}) {
  const formattedProfiles = profiles.map(function(profile) {

    const formatted = chatLib.formatProfile(profile, user, { ...params, nearby: true, includeTags: user?.versionAtLeast('1.13.63') ? true : false}, );

    return formatted;
  });
  return formattedProfiles;
}

function getBaseHash(params) {
  const parts = [];
  if (params.minAge && params.maxAge) {
    parts.push(`age_${params.minAge}-${params.maxAge}`);
  }
  if (params.minAge2 && params.maxAge2) {
    parts.push(`age2_${params.minAge2}-${params.maxAge2}`);
  }
  if (params.sortBestType) {
    parts.push(`sb_${params.sortBestType}`);
  }
  if (params.totalScores && params.totalScores.length > 0) {
    parts.push(`ts_${params.totalScores.join('-')}`);
  }
  if (params.decayedScores && params.decayedScores.length > 0) {
    parts.push(`ds_${params.decayedScores.join('-')}`);
  }
  if (params.interestNames) {
    parts.push('interests');
  }
  if (params.sameCountry) {
    parts.push('sameCountry');
  }
  return parts.join('+');
}
function getLocalLoopHash(user, maxDistance, params) {
  const baseHash = getBaseHash(params);
  return `${baseHash}+lat_${user.latitude2}+long_${user.longitude2}+dist_${maxDistance}`.replaceAll('.', '-');
}
function getCountryLoopHash(countries, params) {
  const baseHash = getBaseHash(params);
  return `${baseHash}+c_${countries.join('-')}`;
}
function getCountryGroupLoopHash(countryGroup, params) {
  const baseHash = getBaseHash(params);
  return `${baseHash}+cg_${countryGroup}`;
}

function sortByVectorSearchScores(data, vectorSearchResultIds) {
  const scoreMap = new Map(vectorSearchResultIds.map(item => [item.user.toString(), item.score]));

  return data.sort((a, b) => {
    const scoreA = scoreMap.get(a._id.toString()) || 0;
    const scoreB = scoreMap.get(b._id.toString()) || 0;
    return scoreB - scoreA;
  });
}

// *********** Begin Atlas Search **************
async function executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, scoreBoosts, vectorSearchResultIds) {
  const compoundQuery = {
    filter: filters,
  };

  if (Array.isArray(vectorSearchResultIds)) {
    if (vectorSearchResultIds.length === 0) {
      return [];
    }
    compoundQuery.filter.unshift({
      in: {
        path: "_id",
        value: vectorSearchResultIds.map(x => x.user),
      },
    });
  }

  let searchPipeline = {
    index: 'users',
    compound: compoundQuery,
    sort: { 'currentDayMetrics.numActionsReceived': 1 },
    returnStoredSource: true,
    concurrent: true,
  };

  if (scoreBoosts && scoreBoosts.length > 0) {
    searchPipeline = {
      index: 'users',
      compound: { ...compoundQuery, should: scoreBoosts },
      returnStoredSource: true,
      concurrent: true,
    };
  }

  let pipeline = [
    {
      $search: searchPipeline,
    },
  ];

  if (!vectorSearchResultIds?.length) {
    pipeline = pipeline.concat([
      {
        $match: {
          _id: { $nin: [user._id].concat(exclusionList) },
        },
      },
    ]);
  }

  pipeline = pipeline.concat([
    { $limit: numProfiles },
    {
      $lookup: {
        from: "users", localField: "_id", foreignField: "_id", as: "document"
      }
    },
    {
      $unwind: "$document"
    },
    {
      $replaceRoot: {
        newRoot: "$document"
      }
    },
    { $project: projections.fullProfileFieldsObj },

  ]);

  const start = new Date().getTime();
  let data = await executeAggregationWithRetry(User, pipeline, {}, { readPreference, replicaTags })
  const end = new Date().getTime();
  console.log(`User ${user._id} Time to run daily profiles query: ${end - start} ms. Found ${data.length} profiles`);

  // Re-order results based on vector search scores when searching with vector search
  if (data.length > 1 && vectorSearchResultIds?.length > 0) {
    data = sortByVectorSearchScores(data, vectorSearchResultIds);
  }

  return data;
}

function getBoostingForProfiles(user, maxDistance, params) {
  let preferences = premiumLib.getPreferences(user);
  const boostingFields = []

  // activeness boosting
  if (!(params.sortBest)) {
    boostingFields.push(...[
      {
        equals: {
          value: 3,
          path: "scores.decayedScore2",
          score: { boost: { value: 3000 } }
        }
      },
      {
        equals: {
          value: 2,
          path: "scores.decayedScore2",
          score: { boost: { value: 2000 } }
        }
      },
      {
        equals: {
          value: 1,
          path: "scores.decayedScore2",
          score: { boost: { value: 1000 } }
        }
      }
    ])
  }

  // total score boosting
  if (params.sortBest || params.sortBestForNewUser) {
    boostingFields.push({
      compound: {
        must: [
          {
            range: {
              path: "metrics.numActionsReceived",
              gte: 30,
            },
          },
          {
            range: {
              path: "scores.likeRatioScore",
              gte: 18,
              lte: 20,
            },
          },
        ],
        score: { boost: { value: 500 } },
      },
    });
  } else {
    boostingFields.push({
      range: {
        path: "scores.totalScore2",
        gte: Math.max(0, user.scores.totalScore2 - 1),
        lte: Math.min(4, user.scores.totalScore2 + 1),
        score: { boost: { value: 500 } }
      }
    });
  }

  // age boosting
  let age = moment().diff(user.birthday, 'years');
  if (age) {

    // +5 years age boosting
    let minBoostAge = Math.max(18, age - 5);
    let maxBoostAge = age + 5;
    if (minBoostAge < maxBoostAge) {
      boostingFields.push({
        range: {
          path: "age",
          gte: minBoostAge,
          lte: maxBoostAge,
          score: { boost: { value: 200 } },
        },
      });
    }

    // +10 years age boosting
    let minBoost10Age = Math.max(18, age - 10);
    let maxBoost10Age = age + 10;

    if (minBoost10Age < age - 5) {
      boostingFields.push({
        range: {
          path: "age",
          gte: minBoost10Age,
          lt: age - 5,
          score: { boost: { value: 100 } },
        },
      });
    }
    if (age + 5 < maxBoost10Age) {
      boostingFields.push({
        range: {
          path: "age",
          gt: age + 5,
          lte: maxBoost10Age,
          score: { boost: { value: 100 } },
        },
      });
    }
  }

  // low numActionsReceived boosting
  for(let i = 0; i < 10; i++) {
  const boostingValue = 50 - i * 5;
  boostingFields.push({
    equals: {
      value: i,
      path: "currentDayMetrics.numActionsReceived",
      score: { boost: { value: boostingValue } }
    }
  })
  }
return boostingFields
}

function buildShowUnspecifiedFilter(fieldName, selectedPreferences) {
  return [{
    compound: {
      should: [
        {
          compound: {
            mustNot: [
              {
                exists: {
                  path: fieldName,
                },
              },
            ],
          },
        },
        {
          in: {
            path: fieldName,
            value: selectedPreferences,
          },
        },
      ],
    },
  }];
}

function buildAttributeFilter(fieldName, selectedPreferences, showUnspecified) {
  if(showUnspecified){
    return buildShowUnspecifiedFilter(fieldName, selectedPreferences);
  }else{
    return [
      {
        in: {
          path: fieldName,
          value: selectedPreferences,
        }
      },
    ]
  }
}

function buildMatchFilterAtlasSearch(user, interplanetaryOnly, params, exhaustedAt, maxDistance) {

  let {
    minAge,
    maxAge,
    minAge2,
    maxAge2,
    totalScores,
    decayedScores,
    interestNames,
    sameCountry,
    userIds,
    sortBestType,
    genderHashFilter,
    lowerDistance,
  } = params;

  let preferences = premiumLib.getPreferences(user);
  let filters = [];

  if (userIds && userIds.length) {
    filters.push({
      in: {
        path: "_id",
        value: userIds,
      }
    });
  }

  if (maxDistance && lowerDistance && user.location) {
    filters.push({
      compound: {
        must: [
          {
            "geoWithin": {
              "circle": {
                "center": user.location,
                "radius": maxDistance * 1609, // miles to meters
              },
              "path": "location"
            }
          },
        ],
        mustNot: [
          {
            "geoWithin": {
              "circle": {
                "center": user.location,
                "radius": lowerDistance * 1609, // miles to meters
              },
              "path": "location"
            }
          },
        ],
      }
    })
  } else if (maxDistance && user.location) {
    filters.push({
      "geoWithin": {
        "circle": {
          "center": user.location,
          "radius": maxDistance * 1609, // miles to meters
        },
        "path": "location"
      }
    });
  }

  if (exhaustedAt && !userIds) {
    filters.push({
      range: {
        path: "profileModifiedAt",
        gt: exhaustedAt,
      }
    });
  }

  if (interestNames && interestNames.length > 0) {
    filters.push({
      in: {
        path: "interestNames",
        value: interestNames,
      }
    });
  }

  // decayed score
  if (decayedScores && decayedScores.length > 0) {
    filters.push({
      in: {
        path: "scores.decayedScore2",
        value: decayedScores,
      }
    });
  }

  // total score
  if (totalScores && totalScores.length > 0) {
    filters.push({
      in: {
        path: "scores.totalScore2",
        value: totalScores,
      }
    });
  }

  // age
  if (minAge2 !== undefined && maxAge !== undefined && minAge2 <= maxAge) {
    maxAge = maxAge2;
    minAge2 = undefined;
    maxAge2 = undefined;
  }
  let ageFilter1;
  let ageFilter2;
  if (minAge !== undefined && maxAge !== undefined) {
    ageFilter1 = {
      range: {
        path: "age",
        gte: minAge,
        lte: maxAge,
      }
    }
  }
  if (minAge2 !== undefined && maxAge2 !== undefined) {
    ageFilter2 = {
      range: {
        path: "age",
        gte: minAge2,
        lte: maxAge2,
      }
    }
  }
  if (ageFilter1 && ageFilter2) {
    filters.push({
      compound: {
        should: [
          ageFilter1,
          ageFilter2,
        ],
      }
    });
  }
  else if (ageFilter1) {
    filters.push(ageFilter1);
  }
  else if (ageFilter2) {
    filters.push(ageFilter2);
  }

  // minAge and maxAge
  let age = moment().diff(user.birthday, 'years');
  filters.push({
    range: {
      path: "preferences.minAge",
      lte: age
    }
  },
  {
    range: {
      path: "preferences.maxAge",
      gte: age,
    }
  });

  /*
  // height
  if (preferences.minHeight || preferences.maxHeight) {
    let heightFilter = { path: "height" };
    if (preferences.minHeight) {
      heightFilter.gte = preferences.minHeight;
    }
    if (preferences.maxHeight) {
      heightFilter.lte = preferences.maxHeight;
    }
    filters = filters.concat([
      {
        range: heightFilter,
      },
    ]);
  }
  */

  // completed profile
  filters.push({
    equals: {
      path: "viewableInDailyProfiles",
      value: true
    }
  });

  if (!userIds) {
    // exclude users who are hiding their location, except when searching for
    // specific users for mutual matching
    filters.push({
      compound: {
        mustNot: [
          {
            equals: {
              path: "hideLocation",
              value: true
            }
          },
        ],
      }
    });
  }

  // num pending reports
  filters.push({
    equals: {
      path: "metrics.numPendingReports",
      value: 0,
    }
  });

  // gender preference
  const genderHash = genderHashFilter || user.genderPreferenceHash;
  const genderPreferences = genderPreferenceLib.getCompatibleGenderPreferenceHashes(genderHash);
  if (genderPreferences && genderPreferences.length > 0) {
    filters.push({
      in: {
        path: "genderPreferenceHash",
        value: genderPreferences,
      }
    });
  }

  // mbti
  if (preferences.personality.length > 0 && preferences.personality.length < 16) {
    filters.push({
      in: {
        path: "personality.mbti",
        value: preferences.personality,
      }
    });
  }

  // country preference
  if (user.countryCode) {
    filters.push({
      compound: {
        should: [
          {
            compound: {
              mustNot: [
                {
                  exists: {
                    path: "preferences.countries",
                  }
                },
              ],
            }
          },
          {
            in: {
              path: 'preferences.countries',
              value: user.countryCode,
            }
          },
        ]
      }
    }, {
      compound: {
        should: [
          {
            compound: {
              mustNot: [
                {
                  equals: {
                    path: "preferences.sameCountryOnly",
                    value: true,
                  }
                },
              ],
            }
          },
          {
            compound: {
              must: [
                {
                  equals: {
                    path: 'preferences.sameCountryOnly',
                    value: true,
                  }
                },
                {
                  in: {
                    path: 'countryCode',
                    value: user.countryCode,
                  }
                },
              ],
            }
          },
        ]
      }
    });
  }

  // country
  const applyCountryFilter = preferences.global && preferences.countries && preferences.countries.length > 0 && !countryLib.shouldRemoveCountryFilter(user);
  if (applyCountryFilter) {
    filters.push({
      in: {
        path: "countryCode",
        value: preferences.countries,
      }
    });
  } else if ((sameCountry || preferences.sameCountryOnly) && user.countryCode) {
    filters.push({
      in: {
        path: "countryCode",
        value: user.countryCode,
      }
    });
  } else {
    // India and Philippines users should be isolated
    const countryCode = countryLib.shouldRemoveCountryFilter(user);
    if (countryCode) {
      filters.push({
        in: {
          path: "countryCode",
          value: countryCode,
        }
      });
    } else {
      filters.push({
        compound: {
          mustNot: [
            {
              in: {
                path: "countryCode",
                value: ['IN','PH'],
              }
            }
          ]
        }
      });
    }
  }

  if (preferences.interestNames && preferences.interestNames.length > 0) {
    filters.push(...buildAttributeFilter('interestNames', preferences.interestNames, preferences.showUnspecified?.interestNames))
  }

  if (preferences.excludedInterestNames?.length){
    let filteredArray = basicLib.subtractArrays(preferences.excludedInterestNames, preferences.interestNames)
    if(filteredArray.length > 0){
      filters.push({
        compound: {
          mustNot: [
            {
              in: {
                path: "interestNames",
                value: filteredArray,
              }
            }
          ]
        }
      });
    }
  }

  if (preferences.enneagrams && preferences.enneagrams.length > 0 && preferences.enneagrams.length < enneagrams.length) {
    filters.push(...buildAttributeFilter('enneagram', preferences.enneagrams, preferences.showUnspecified?.enneagrams))
  }

  if (preferences.relationshipStatus && preferences.relationshipStatus.length > 0) {
    filters.push(...buildAttributeFilter('relationshipStatus', preferences.relationshipStatus, preferences.showUnspecified?.relationshipStatus))
  }

  if (preferences.datingSubPreferences && preferences.datingSubPreferences.length > 0) {
    filters.push(...buildAttributeFilter('datingSubPreferences', preferences.datingSubPreferences, preferences.showUnspecified?.datingSubPreferences))
  }

  if (preferences.relationshipType && preferences.relationshipType.length > 0) {
    filters.push(...buildAttributeFilter('relationshipType', preferences.relationshipType, preferences.showUnspecified?.relationshipType))
  }

  if (preferences.ethnicities && preferences.ethnicities.length > 0 && preferences.ethnicities.length < ethnicities.length) {
    filters.push(...buildAttributeFilter('ethnicities', preferences.ethnicities, preferences.showUnspecified?.ethnicities))
  }

  for (let key in moreAboutUserChoices) {
    if (preferences[key] && preferences[key].length > 0 && preferences[key].length < moreAboutUserChoices[key].length) {
      filters.push(...buildAttributeFilter("moreAboutUser." + key, preferences[key], preferences.showUnspecified && preferences.showUnspecified[key]))
    }
  }

  if (preferences.sexuality && preferences.sexuality.length > 0) {
    filters.push(...buildAttributeFilter('sexuality', preferences.sexuality, preferences.showUnspecified?.sexuality))
  }

  if (preferences.languages && preferences.languages.length > 0 && preferences.languages.length < languageCodes.length) {
    filters.push(...buildAttributeFilter('languages', preferences.languages, preferences.showUnspecified?.languages))
  }

  if (preferences.horoscopes && preferences.horoscopes.length > 0 && preferences.horoscopes.length < horoscopes.length) {
    filters.push(...buildAttributeFilter('horoscope', preferences.horoscopes, preferences.showUnspecified?.horoscopes))
    filters.push({
      compound: {
        mustNot: [
          {
            equals: {
              path: "hideHoroscope",
              value: true
            }
          },
        ],
      }
    })
  }

  if (preferences.bioLength > 0) {
    filters.push({
      range: {
        path: "scores.descriptionAndPromptsLength",
        gte: preferences.bioLength,
      }
    });
  }

  if (preferences.keywords && preferences.keywords.length > 0) {
    filters.push({
      in: {
        path: "keywords",
        value: preferences.keywords.map((x) => x.toLowerCase()),
      }
    });
  }

  if (constants.hideUnverifiedUsers() || (preferences.showVerifiedOnly && user.isVerified())) {
    filters.push({
      in: {
        path: "verification.status",
        value: [
          'verified',
          'reverifying',
        ],
      }
    });
  }

  if (!user.isVerified()) {
    filters.push({
      compound: {
        mustNot: [
          {
            equals: {
              path: "preferences.showToVerifiedOnly",
              value: true
            }
          },
        ],
      }
    })
  }

  if (interplanetaryOnly || maxDistance > 100) {
    filters.push({
      equals: {
        path: "preferences.global",
        value: true
      }
    });
  }

  if (user.keywords && user.keywords.length > 0) {
    filters.push({
      compound: {
        mustNot: [
          {
            in: {
              path: "hideFromKeywords",
              value: user.keywords,
            }
          },
        ],
      }
    });
  }
  if (user.hideFromKeywords && user.hideFromKeywords.length > 0) {
    filters.push({
      compound: {
        mustNot: [
          {
            in: {
              path: "keywords",
              value: user.hideFromKeywords,
            }
          },
        ],
      }
    });
  }

  if (user.location) {
    filters.push({
      compound: {
        should: [
          {
            compound: {
              mustNot: [
                {
                  equals: {
                    path: "hideFromNearby",
                    value: true
                  }
                },
              ],
            }
          },
          {
            compound: {
              must: [
                {
                  equals: {
                    path: "hideFromNearby",
                    value: true
                  }
                },
              ],
              mustNot: [
                {
                  "geoWithin": {
                    "circle": {
                      "center": user.location,
                      "radius": constants.hideFromNearbyDistance * 1609, // miles to meters
                    },
                    "path": "location"
                  }
                },
              ],
            }
          }
        ],
      }
    });
  }
  if (user.location && (user.hideFromNearby || params.minDistanceForCarrotAlgo)) {
    let limitDistance;
    if (user.hideFromNearby && params.minDistanceForCarrotAlgo) {
      limitDistance = Math.max(constants.hideFromNearbyDistance, params.minDistanceForCarrotAlgo);
    } else if (params.minDistanceForCarrotAlgo) {
      limitDistance = params.minDistanceForCarrotAlgo;
    } else {
      limitDistance = constants.hideFromNearbyDistance;
    }
    limitDistance = limitDistance * 1609;  // miles to meters
    filters.push({
      compound: {
        mustNot: [
          {
            "geoWithin": {
              "circle": {
                "center": user.location,
                "radius": limitDistance,
              },
              "path": "location"
            }
          },
        ],
      }
    });
  }

  // incoming requests preferences
  filters.push({
    compound: {
      should: [
        {
          compound: {
            mustNot: [
              {
                equals: {
                  path: "incomingRequestsPreferences.sameCountryOnly",
                  value: true,
                }
              },
            ],
          }
        },
        {
          compound: {
            must: [
              {
                equals: {
                  path: 'incomingRequestsPreferences.sameCountryOnly',
                  value: true,
                }
              },
              {
                in: {
                  path: 'countryCode',
                  value: user.countryCode || 'None',
                }
              },
            ],
          }
        },
      ]
    }
  },
  {
    compound: {
      should: [
        {
          compound: {
            mustNot: [
              {
                equals: {
                  path: "incomingRequestsPreferences.customActivated",
                  value: true,
                }
              },
            ],
          }
        },
        {
          compound: {
            must: [
              {
                equals: {
                  path: 'incomingRequestsPreferences.customActivated',
                  value: true,
                }
              },
              {
                in: {
                  path: 'incomingRequestsPreferences.custom.genderPreferenceHash',
                  value: genderPreferences.length > 0 ? genderPreferences : 'None',
                }
              },
              {
                range: {
                  path: "incomingRequestsPreferences.custom.minAge",
                  lte: age
                }
              },
              {
                range: {
                  path: "incomingRequestsPreferences.custom.maxAge",
                  gte: age,
                }
              },
              {
                compound: {
                  should: [
                    {
                      compound: {
                        mustNot: [
                          {
                            exists: {
                              path: "incomingRequestsPreferences.custom.countries",
                            }
                          },
                        ],
                      }
                    },
                    {
                      in: {
                        path: 'incomingRequestsPreferences.custom.countries',
                        value: user.countryCode || 'None',
                      }
                    },
                  ]
                }
              },
            ],
          }
        },
      ]
    }
  });

  if (sortBestType == 'top') {
    filters.push({
      range: {
        path: 'scores.likeRatioScore',
        gte: 18
      }
    },
    {
      range: {
        path: 'metrics.numActionsReceived',
        gte: 30
      }
    });
  } else if (sortBestType == 'remaining') {
    filters.push({
      compound: {
        should: [
          {
            range: {
              path: 'scores.likeRatioScore',
              lt: 18
            }
          },
          {
            range: {
              path: 'metrics.numActionsReceived',
              lt: 30
            }
          }
        ],
      }
    });
  }

  return filters;
}

function useAtlasSearch(user) {
  //return user.isConfigTrue('atlas_search');

  if (process.env.NODE_ENV == 'beta' || process.env.NODE_ENV == 'prod' || process.env.NODE_ENV == 'aifilter_carneless') {
    return true;
  }

  return false;
}

async function countNumLocalUsersTargetGender(user) {
  if (!useAtlasSearch(user) || !user.location) {
    return;
  }

  const genderPreferences = genderPreferenceLib.getCompatibleGenderPreferenceHashes(user.genderPreferenceHash);
  if (!genderPreferences || !genderPreferences.length) {
    return;
  }

  let preferences = premiumLib.getPreferences(user);
  let filters = [];

  // completed profile
  filters.push({
    equals: {
      path: "viewableInDailyProfiles",
      value: true
    }
  });

  // within 50 miles
  filters.push({
    "geoWithin": {
      "circle": {
        "center": user.location,
        "radius": 50 * 1609, // miles to meters
      },
      "path": "location"
    }
  });

  // gender preference
  filters.push({
    in: {
      path: "genderPreferenceHash",
      value: genderPreferences,
    }
  });

  let pipeline = [
    {
      $searchMeta: {
        index: 'users',
        count: {
          type: 'total',
        },
        compound: {
          filter: filters,
        },
      }
    },
  ];

  const data = await executeAggregationWithRetry(User, pipeline, {}, { readPreference, replicaTags })

  if (data) {
    user.localStats.numLocalUsersTargetGender = data[0].count.total;
    await user.save();
  }
}

// *********** End Atlas Search **************

async function getVectorSearchResults(user, embeddingAifilter, exclusionList, maxDistance, countryCodes, candidateUsers) {
  const start = new Date();
  const filter = {
    hideFromVisionSearch: { $ne: true },
  };

  const exclusionIds = [user._id, ...(exclusionList || [])];
  if (candidateUsers?.length) {
    filter.user = {
      $in: candidateUsers.map(x => x._id),
      ...(exclusionIds.length && { $nin: exclusionIds }),
    };
  } else if (exclusionIds.length) {
    filter.user = { $nin: exclusionIds };
  }

  if (user.location && maxDistance) {
    const [lngMin, latMin, lngMax, latMax] = calculateBoundingBox(user.location?.coordinates, maxDistance) || [];
    if (lngMin && latMin && lngMax && latMax) {
      filter.latitude = { $gte: latMin, $lte: latMax };
      filter.longitude = { $gte: lngMin, $lte: lngMax };
    }
  }

  if (countryCodes && countryCodes.length) {
    filter.countryCode = { $in: countryCodes };
  }

  const pipeline = [
    {
      $vectorSearch: {
        index: "vector_index",
        queryVector: embeddingAifilter,
        path: "merged_embedding",
        numCandidates: 10000,
        limit: 1000,
        filter,
      },
    },
    {
      $project: {
        user: 1,
        score: { $meta: "vectorSearchScore" },
        _id: 0,
      },
    },
  ];

  const vectorResults = (await UserEmbeddings.aggregate(pipeline)).filter(x => x.score > 0.60);
  console.log(`User ${user._id} Time to get vector search results: ${new Date() - start} ms. Found ${vectorResults.length} results.`);
  return vectorResults;
}

function buildMatchFilter(user, exclusionList, interplanetaryOnly, params, exhaustedAt, maxDistance) {
  const {
    minAge,
    maxAge,
    minAge2,
    maxAge2,
    totalScores,
    decayedScores,
    interestNames,
    sameCountry,
    userIds,
    sortBestType,
    genderHashFilter,
  } = params;

  const preferences = premiumLib.getPreferences(user);
  let filters = {};

  if (maxDistance) {
    filters = locationLib.getLatLongQuery(user.location, maxDistance);
  }

  if (exhaustedAt && !userIds) {
    filters['profileModifiedAt'] = { $gt: exhaustedAt };
  }

  if (interestNames && interestNames.length > 0) {
    filters.interestNames = { $in: interestNames };
  }

  // decayed score
  if (decayedScores && decayedScores.length > 0) {
    filters['scores.decayedScore2'] = { $in: decayedScores };
  }

  // total score
  if (totalScores && totalScores.length > 0) {
    filters['scores.totalScore2'] = { $in: totalScores };
  }

  const ageFilters = [];
  if (minAge2 && maxAge2) {
    for (let i = minAge2; i <= maxAge2 && i <= 100; i++) {
      ageFilters.push(i);
    }
  }
  if (minAge && maxAge) {
    for (let i = minAge; i <= maxAge && i <= 100; i++) {
      ageFilters.push(i);
    }
  }
  if (ageFilters.length > 0) {
    filters.age = { $in: ageFilters };
  }

  filters['viewableInDailyProfiles'] = true;

  if (!userIds) {
    // exclude users who are hiding their location, except when searching for
    // specific users for mutual matching
    filters['hideLocation'] = { $ne: true };
  }

  filters['metrics.numPendingReports'] = 0;

  const age = moment().diff(user.birthday, 'years');
  filters['preferences.maxAge'] = { $gte: age, $lte: 200 };
  filters['preferences.minAge'] = { $gte: 18, $lte: age };
  const genderHash = genderHashFilter || user.genderPreferenceHash;
  const genderPreferences = genderPreferenceLib.getCompatibleGenderPreferenceHashes(genderHash);
  filters['genderPreferenceHash'] = { $in: genderPreferences };

  if (preferences.personality.length > 0 && preferences.personality.length < 16) {
    filters['personality.mbti'] = { $in: preferences.personality };
  }

  filters['preferences.countries'] = { $in: [null, user.countryCode] };

  const applyCountryFilter = preferences.global && preferences.countries && preferences.countries.length > 0 && !countryLib.shouldRemoveCountryFilter(user);
  if (applyCountryFilter) {
    filters.countryCode = { $in: preferences.countries };
  } else if (sameCountry || preferences.sameCountryOnly) {
    filters.countryCode = user.countryCode;
  } else {
    // India and Philippines users should be isolated
    const countryCode = countryLib.shouldRemoveCountryFilter(user);
    if (countryCode) {
      filters.countryCode = countryCode;
    } else {
      filters.countryCode = { $nin: ['IN','PH'] };
    }
  }

  if (preferences.interestNames && preferences.interestNames.length > 0) {
    if(!filters['interestNames']) filters['interestNames'] = {}
    filters['interestNames']['$in'] = preferences.interestNames
  }
  if (preferences.excludedInterestNames?.length){
    let filteredArray = basicLib.subtractArrays(preferences.excludedInterestNames, preferences.interestNames)
    if(filteredArray.length > 0){
      if(!filters['interestNames']) filters['interestNames'] = {}
      filters['interestNames']['$nin'] = filteredArray
    }
  }
  if (preferences.enneagrams && preferences.enneagrams.length > 0 && preferences.enneagrams.length < enneagrams.length) {
    if(preferences.showUnspecified?.enneagrams){
      filters.enneagram = { $in: [...preferences.enneagrams, null] };
    }else{
      filters.enneagram = { $in: preferences.enneagrams };
    }
  }
  if (preferences.relationshipStatus && preferences.relationshipStatus.length > 0) {
    if(preferences.showUnspecified?.relationshipStatus){
      filters.relationshipStatus = { $in: [...preferences.relationshipStatus, null] };
    }else{
      filters.relationshipStatus = { $in: preferences.relationshipStatus };
    }
  }
  if (preferences.datingSubPreferences && preferences.datingSubPreferences.length > 0) {
    if(preferences.showUnspecified?.datingSubPreferences){
      filters.datingSubPreferences = { $in: [...preferences.datingSubPreferences, null] };
    }else{
      filters.datingSubPreferences = { $in: preferences.datingSubPreferences };
    }
  }
  if (preferences.relationshipType && preferences.relationshipType.length > 0) {
    if(preferences.showUnspecified?.relationshipType){
      filters.relationshipType = { $in: [...preferences.relationshipType, null] };
    }else{
      filters.relationshipType = { $in: preferences.relationshipType };
    }
  }

  if (preferences.sexuality && preferences.sexuality.length > 0) {
    if(preferences.showUnspecified?.sexuality){
      filters.sexuality = { $in: [...preferences.sexuality, null] };
    }else{
      filters.sexuality = { $in: preferences.sexuality };
    }
  }
  if (preferences.languages && preferences.languages.length > 0 && preferences.languages.length < languageCodes.length) {
    filters.languages = { $in: preferences.languages };
  }
  if (preferences.ethnicities && preferences.ethnicities.length > 0 && preferences.ethnicities.length < ethnicities.length) {
    if(preferences.showUnspecified?.ethnicities){
      filters.ethnicities = { $in: [...preferences.ethnicities, null] };
    }else{
      filters.ethnicities = { $in: preferences.ethnicities };
    }
  }
  if (preferences.horoscopes && preferences.horoscopes.length > 0 && preferences.horoscopes.length < horoscopes.length) {
    filters.horoscope = { $in: preferences.horoscopes };
    filters.hideHoroscope = { $ne: true }
  }

  /*
  // height
  if (preferences.minHeight || preferences.maxHeight) {
    let heightFilter = {};
    if (preferences.minHeight) {
      heightFilter.$gte = preferences.minHeight;
    }
    if (preferences.maxHeight) {
      heightFilter.$lte = preferences.maxHeight;
    }
    filters['height'] = heightFilter;
  }
  */

  if (preferences.bioLength > 0) {
    filters['scores.descriptionAndPromptsLength'] = { $gte: preferences.bioLength };
  }

  if (preferences.keywords && preferences.keywords.length > 0) {
    filters.keywords = { $in: preferences.keywords.map((x) => x.toLowerCase()) };
  }

  for (const key in moreAboutUserChoices) {
    if (preferences[key] && preferences[key].length > 0 && preferences[key].length < moreAboutUserChoices[key].length) {
      if(preferences.showUnspecified && preferences.showUnspecified[key]){
        filters[`moreAboutUser.${key}`] = { $in: [...preferences[key], null] };
      }else{
        filters[`moreAboutUser.${key}`] = { $in: preferences[key] };
      }
    }
  }

  if (constants.hideUnverifiedUsers() || (preferences.showVerifiedOnly && user.isVerified())) {
    filters['verification.status'] = 'verified';
  }

  if (!user.isVerified()) {
    filters['preferences.showToVerifiedOnly'] = { $ne: true };
  }

  if (interplanetaryOnly || maxDistance > 100) {
    filters['preferences.global'] = true;
  }

  if (user.keywords && user.keywords.length > 0) {
    filters['hideFromKeywords'] = { $nin: user.keywords };
  }
  if (user.hideFromKeywords) {
    if (filters.keywords) {
      filters.keywords['$nin'] = user.hideFromKeywords;
    }
    else {
      filters['keywords'] = { $nin: user.hideFromKeywords };
    }
  }

  if (user.location) {
    const nearbyQuery = locationLib.getLatLongQuery(user.location, constants.hideFromNearbyDistance);
    filters['$or'] = [
      { hideFromNearby: { $ne: true } },
      {
        hideFromNearby: true,
        latitude2: { $not: nearbyQuery.latitude2 },
        longitude2: { $not: nearbyQuery.longitude2 },
      },
    ];
  }
  if (user.location && (user.hideFromNearby || params.minDistanceForCarrotAlgo)) {
    let limitDistance;
    if (user.hideFromNearby && params.minDistanceForCarrotAlgo) {
      limitDistance = Math.max(constants.hideFromNearbyDistance, params.minDistanceForCarrotAlgo);
    } else if (params.minDistanceForCarrotAlgo) {
      limitDistance = params.minDistanceForCarrotAlgo;
    } else {
      limitDistance = constants.hideFromNearbyDistance;
    }
    const nearbyQuery = locationLib.getLatLongQuery(user.location, limitDistance);
    if (filters.latitude2) {
      filters.latitude2['$not'] = nearbyQuery.latitude2;
    } else {
      filters.latitude2 = { $not: nearbyQuery.latitude2 };
    }
    if (filters.longitude2) {
      filters.longitude2['$not'] = nearbyQuery.longitude2;
    } else {
      filters.longitude2 = { $not: nearbyQuery.longitude2 };
    }
  }

  filters._id = { $nin: [user._id].concat(exclusionList) };

  if (userIds && userIds.length) {
    filters._id.$in = userIds;
  }

  if (sortBestType == 'top') {
    filters['scores.likeRatioScore'] = { $gte: 18 }
    filters['metrics.numActionsReceived'] = { $gte: 30 }
  } else if (sortBestType == 'remaining') {
    filters['$and'] = filters['$and'] || []; //not to override the old $or and $and conditions
    filters['$and'].push({
      $or: [
        { 'scores.likeRatioScore': { $lt: 18 } },
        { 'metrics.numActionsReceived': { $lt: 30 } }
      ]
    });
  }

  return filters;
}

async function executeQuery(user, query, numProfiles) {
  let pipeline = [
    { $match: query },
  ];

  pipeline = pipeline.concat([
    { $limit: numProfiles },
    { $project: projections.fullProfileFieldsObj },

  ]);

  return await User.aggregate(pipeline).read(readPreference, replicaTags);
}

async function getCountryProfilesFn(user, exclusionList, numProfiles, params, countries, scoreBoosts) {
  if (numProfiles == 0) {
    return [];
  }

  const loopHash = getCountryLoopHash(countries, params);
  const exhaustedAt = user.recommendationsExhaustedAt || user.countryLoops.get(loopHash);

  let allProfiles;
  if (useAtlasSearch(user)) {
    let filters = buildMatchFilterAtlasSearch(user, true, params, exhaustedAt);
    filters = filters.concat([
      {
        in: {
          path: "countryCode",
          value: countries,
        }
      },
    ]);
    allProfiles = await executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, scoreBoosts, params.vectorSearchResultIds);
  } else {
    const query = {
      countryCode: { $in: countries },
      ...buildMatchFilter(user, exclusionList, true, params, exhaustedAt),
    };
    allProfiles = await executeQuery(user, query, numProfiles);
  }

  if (allProfiles.length == 0 && !params.sortBest && !params.userIds?.length && !params.prefetch && !params.refAifilter) {
    user.countryLoops.set(loopHash, new Date());
  }

  return allProfiles;
}

// gets profiles from the same country group using the default country group filter
async function getCountryGroupProfilesFn(user, exclusionList, numProfiles, params, scoreBoosts) {
  if (numProfiles == 0) {
    return [];
  }

  const exhaustedAt = user.recommendationsExhaustedAt;

  let allProfiles;
  if (useAtlasSearch(user)) {
    let filters = buildMatchFilterAtlasSearch(user, true, params, exhaustedAt);
    allProfiles = await executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, scoreBoosts, params.vectorSearchResultIds);
  } else {
    const query = buildMatchFilter(user, exclusionList, true, params, exhaustedAt);
    allProfiles = await executeQuery(user, query, numProfiles);
  }

  return allProfiles;
}

async function getInterplanetaryProfiles(user, exclusionList, numProfiles, params) {
  if (numProfiles == 0) {
    return [];
  }

  let allProfiles = [];

  if (user.preferences.countries && user.preferences.countries.length > 0 && !countryLib.shouldRemoveCountryFilter(user)) {
    allProfiles = allProfiles.concat(
      await getProfilesBoostedLoop(
        getCountryProfilesFn,
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        user.preferences.countries,
      ),
    );
    return allProfiles;
  }

  // get from same country tier
  allProfiles = allProfiles.concat(
    await getProfilesBoostedLoop(
      getCountryGroupProfilesFn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
    ),
  );

  return allProfiles;
}

async function getInterplanetaryProfilesV2(user, exclusionList, numProfiles, params) {
  if (numProfiles == 0) {
    return [];
  }

  let allProfiles = [];

  const scoreBoosts = getBoostingForProfiles(user, null, params);
  if (user.preferences.countries && user.preferences.countries.length > 0 && !countryLib.shouldRemoveCountryFilter(user)) {
    allProfiles = allProfiles.concat(
      await getCountryProfilesFn(
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        user.preferences.countries,
        scoreBoosts,
      ),
    );
    return allProfiles;
  }

  // get from same country tier
  allProfiles = allProfiles.concat(
    await getCountryGroupProfilesFn(
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      scoreBoosts,
    ),
  );

  return allProfiles;
}

async function getProfilesBoostedLoop(fn, user, exclusionList, numProfiles, params, args) { //use this loop function to boost between distance and activeness
  if (numProfiles == 0) {
    return [];
  }
  const boostedParams = { ...params }
  let allProfiles = [];
  
  allProfiles = allProfiles.concat(
    await getProfilesActiveLoop(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      boostedParams, // to avoid changes inside getProfilesActiveLoop in params object from loop functions doesn't come back to getProfilesBoostedLoop,
      args,
    ),
  );

  return allProfiles;
}

async function getProfilesActiveLoop(fn, user, exclusionList, numProfiles, params, args) {
  if (numProfiles == 0) {
    return [];
  }

  let allProfiles = [];

  allProfiles = allProfiles.concat(
    await getActiveProfiles(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  if (params.mostRelevant) {
    return allProfiles;
  }

  allProfiles = allProfiles.concat(
    await getInactiveProfiles(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  return allProfiles;
}

async function getActiveProfiles(fn, user, exclusionList, numProfiles, params, args) {
  let allProfiles = [];

  if (params.sortBest) {
    params.decayedScores = [0, 1, 2, 3];
    allProfiles = allProfiles.concat(
      await getProfilesScoreLoop(
        fn,
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        args,
      ),
    );
    return allProfiles;
  }

  params.decayedScores = [3];
  allProfiles = allProfiles.concat(
    await getProfilesScoreLoop(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  params.decayedScores = [2];
  allProfiles = allProfiles.concat(
    await getProfilesScoreLoop(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  params.decayedScores = [1];
  allProfiles = allProfiles.concat(
    await getProfilesScoreLoop(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  return allProfiles;
}

async function getInactiveProfiles(fn, user, exclusionList, numProfiles, params, args) {
  if (params.sortBest) {
    return [];
  }
  params.decayedScores = [0];
  return await getProfilesScoreLoop(fn, user, exclusionList, numProfiles, params, args);
}

async function getProfilesScoreLoop(fn, user, exclusionList, numProfiles, params, args) {
  if (numProfiles == 0) {
    return [];
  }

  let allProfiles = [];

  if (params.sortBest || params.sortBestForNewUser) {
    params.sortBestType = 'top'
    allProfiles = allProfiles.concat(
      await getProfilesAgeLoop(
        fn,
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        args,
      ),
    );

    if (params.mostRelevant) {
      return allProfiles;
    }
    params.sortBestType = 'remaining'
    allProfiles = allProfiles.concat(
      await getProfilesAgeLoop(
        fn,
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        args,
      ),
    );

    return allProfiles;
  }

  const possibleScores = [0, 1, 2, 3, 4];
  const userScore = user.scores.totalScore2;
  const nearbyScores = [userScore - 1, userScore, userScore + 1];

  params.totalScores = nearbyScores.filter(x => possibleScores.includes(x));
  allProfiles = allProfiles.concat(
    await getProfilesAgeLoop(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  if (params.mostRelevant) {
    return allProfiles;
  }

  params.totalScores = possibleScores.filter(x => !nearbyScores.includes(x));
  allProfiles = allProfiles.concat(
    await getProfilesAgeLoop(
      fn,
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  return allProfiles;
}

async function getProfilesAgeLoop(fn, user, exclusionList, numProfiles, params, args) {
  if (numProfiles == 0) {
    return [];
  }

  let allProfiles = [];

  const age = moment().diff(user.birthday, 'years');

  if (age < user.preferences.minAge || age > user.preferences.maxAge) {
    allProfiles = allProfiles.concat(
      await fn(
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        args,
      ),
    );
    return allProfiles;
  }

  params.minAge = age;
  params.maxAge = age;
  params.minAge2 = age;
  params.maxAge2 = age;

  const numLoops = 2;
  for (let i = 0; i < numLoops; i++) {
    if (allProfiles.length >= numProfiles) {
      break;
    }

    if (params.minAge !== undefined) {
      if (params.minAge <= user.preferences.minAge) {
        params.minAge = undefined;
        params.maxAge = undefined;
      } else {
        params.maxAge = params.minAge;
        params.minAge = Math.max(user.preferences.minAge, params.minAge - 5);
        if (i == numLoops - 1) {
          params.minAge = user.preferences.minAge;
        }
      }
    }

    if (params.maxAge2 !== undefined) {
      if (params.maxAge2 >= user.preferences.maxAge) {
        params.minAge2 = undefined;
        params.maxAge2 = undefined;
      } else {
        params.minAge2 = params.maxAge2;
        params.maxAge2 = Math.min(user.preferences.maxAge, params.maxAge2 + 5);
        if (i == numLoops - 1 || params.maxAge2 >= 50) {
          params.maxAge2 = user.preferences.maxAge;
        }
      }
    }

    if (!params.minAge && !params.maxAge
      && !params.minAge2 && !params.maxAge2) {
      break;
    }

    allProfiles = allProfiles.concat(
      await getProfilesInterestLoop(
        fn,
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        args,
      ),
    );

    if (params.mostRelevant) {
      return allProfiles;
    }
  }

  return allProfiles;
}

async function getProfilesInterestLoop(fn, user, exclusionList, numProfiles, params, args) {
  if (numProfiles == 0) {
    return [];
  }

  let allProfiles = [];

  /*
  if (user.interestNames?.length && !user.preferences.interestNames?.length) {
    params.interestNames = user.interestNames;
    allProfiles = allProfiles.concat(
      await fn(
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
        args,
      ),
    );
  }
  */

  params.interestNames = null;
  allProfiles = allProfiles.concat(
    await fn(
      user,
      allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
      numProfiles - allProfiles.length,
      params,
      args,
    ),
  );

  return allProfiles;
}

async function getLocalProfilesFn(user, exclusionList, numProfiles, params, maxDistance, scoreBoosts) {
  if (numProfiles == 0 || !user.location) {
    return [];
  }

  const loopHash = getLocalLoopHash(user, maxDistance, params);
  const exhaustedAt = user.recommendationsExhaustedAt || user.localLoops.get(loopHash);

  let allProfiles;
  if (useAtlasSearch(user)) {
    let filters = buildMatchFilterAtlasSearch(user, false, params, exhaustedAt, maxDistance);
    allProfiles = await executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, scoreBoosts, params.vectorSearchResultIds);
  } else {
    const query = buildMatchFilter(user, exclusionList, false, params, exhaustedAt, maxDistance);
    allProfiles = await executeQuery(user, query, numProfiles);
  }
  if (allProfiles.length == 0 && !params.sortBest && !params.userIds?.length && !params.prefetch && !params.refAifilter) {
    user.localLoops.set(loopHash, new Date());
  }

  return allProfiles;
}

async function getProfilesHelperV3(
  user,
  numProfiles,
  isInterplanetary,
  maxDistance,
  exclusionList,
  params,
) {
  if (numProfiles == 0) {
    return [];
  }

  try {
    let allProfiles = [];
    let useBoostAlgorithm = false;
    if (process.env.NODE_ENV === 'prod' && exclusionList?.length > 50000) { // gradually decreasing exclusion size to implement the new logic to all profiles
      useBoostAlgorithm = true;
    } else if (user.isConfigTrue('use_boost_algorithm_for_daily_profiles') || process.env.NODE_ENV === 'aifilter_carneless') {
      useBoostAlgorithm = true;
    }

    if (useBoostAlgorithm) {
      allProfiles = await getProfilesHelperV4(user, numProfiles, isInterplanetary, maxDistance, exclusionList, params);
      return allProfiles;
    }
    // local profiles
    if (user.preferences.local !== false && user.location) {
      if (user.preferences.global) {
        maxDistance = 12500;
      }

      const start = new Date().getTime();
      if (params.embeddingAifilter && useAtlasSearch(user)) {
        const maxDistanceVectorSearch = [5, 15, 25].reduce((acc, n) => (acc >= maxDistance ? acc : Math.min(n, maxDistance)), 0);
        params.vectorSearchResultIds = await getVectorSearchResults(user, params.embeddingAifilter, exclusionList, maxDistanceVectorSearch);
      }
      // show most relevant profiles first prioritized by distance
      params.mostRelevant = true;
      params.sameCountry = true;
      for (let distance of [5, 15, 25]) {
        distance = Math.min(distance, maxDistance);
        allProfiles = allProfiles.concat(
          await getProfilesBoostedLoop(
            getLocalProfilesFn,
            user,
            allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
            numProfiles - allProfiles.length,
            params,
            distance,
          ),
        );
        if (distance >= maxDistance) {
          break;
        }
      }

      // show all profiles prioritized by distance
      params.mostRelevant = false;
      params.sameCountry = false;
      if (params.embeddingAifilter && useAtlasSearch(user)) {
        const maxDistanceVectorSearch = [50, 250, 500, 2000].reduce((acc, n) => (acc >= maxDistance ? acc : Math.min(n, maxDistance)), 0);
        params.vectorSearchResultIds = await getVectorSearchResults(user, params.embeddingAifilter, exclusionList, maxDistanceVectorSearch);
      }
      for (let distance of [50, 250, 500, 2000]) {
        distance = Math.min(distance, maxDistance);
        allProfiles = allProfiles.concat(
          await getProfilesBoostedLoop(
            getLocalProfilesFn,
            user,
            allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
            numProfiles - allProfiles.length,
            params,
            distance,
          ),
        );
        if (distance >= maxDistance) {
          break;
        }
      }

      const end = new Date().getTime();
      console.log(`User ${user._id} Time to get local profiles: ${end-start} ms`);
    }

    // not interplanetary - cannot expand search
    if (!isInterplanetary) {
      return allProfiles;
    }

    // interplanetary
    const start = new Date().getTime();
    if (params.embeddingAifilter && useAtlasSearch(user)) {
      params.vectorSearchResultIds = await getVectorSearchResults(user, params.embeddingAifilter, exclusionList);
    }
    allProfiles = allProfiles.concat(
      await getInterplanetaryProfiles(
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
      ),
    );
    const end = new Date().getTime();
    console.log(`User ${user._id} Time to get global profiles: ${end-start} ms`);

    return allProfiles;
  }
  catch (err) {
    if (err?.message?.includes('BSONObj size')) {
      user.sizeOfExclusionUsedDuringFailure = exclusionList.length || 0;
      user.exclusionListFailed = true;
      await user.save();
      await actionLib.recalculateExclusionList(user._id, true);
    }
    throw err;
  }
}

async function getProfilesHelperV4(
  user,
  numProfiles,
  isInterplanetary,
  maxDistance,
  exclusionList,
  params,
) {
  if (numProfiles == 0) {
    return [];
  }

  try {
    let allProfiles = [];
    // local profiles
    if (user.preferences.local !== false && user.location) {
      if (user.preferences.global) {
        maxDistance = 12500;
      }
      let distanceLoop = [5, 15, 25];
      if (params.embeddingAifilter && useAtlasSearch(user)) {
        const maxDistanceVectorSearch = [5, 15, 25].reduce((acc, n) => (acc >= maxDistance ? acc : Math.min(n, maxDistance)), 0);
        params.vectorSearchResultIds = await getVectorSearchResults(user, params.embeddingAifilter, exclusionList, maxDistanceVectorSearch);
      }
      params.sameCountry = true;
      for(let k = 0; k < distanceLoop.length; k++) {
        if(distanceLoop[k] == 5) {
          params.lowerDistance = 0
        } else {
          params.lowerDistance = distanceLoop[k-1]
        }
        const distance = Math.min(distanceLoop[k], maxDistance);
        const scoreBoosts = getBoostingForProfiles(user, maxDistance, params);
        const start = new Date().getTime();
        allProfiles = allProfiles.concat(
          await getLocalProfilesFn(
            user,
            allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
            numProfiles - allProfiles.length,
            params,
            distance,
            scoreBoosts,
          ),
        );
        const end = new Date().getTime();
        console.log(`User ${user._id} Time to get local profiles with distance ${distance} is: ${end-start} ms`);
        if (distance >= maxDistance) {
          break;
        }
      }

      distanceLoop = [50, 250, 500, 1500, 2500, 3500, 4500, 5500, 6500, 7500, 8500, 9500, 10500, 11500, 12500];

      // based on earth radius, 3500 miles can be highest maxDistance for the bounding box, more than that will be searched without any location boundary
      if (params.embeddingAifilter && useAtlasSearch(user)) {
        let maxDistanceVectorSearch = [50, 250, 500, 1500, 2500, 3500].reduce((acc, n) => (acc >= maxDistance ? acc : Math.min(n, maxDistance)), 0);
        if (maxDistance > 3500) {
          maxDistanceVectorSearch = undefined;
        }
        params.vectorSearchResultIds = await getVectorSearchResults(user, params.embeddingAifilter, exclusionList, maxDistanceVectorSearch);
      }
      params.sameCountry = false;
      for(let k = 0; k < distanceLoop.length; k++) {
        if (distanceLoop[k] == 50) {
          params.lowerDistance = 0
        } else {
          params.lowerDistance = distanceLoop[k-1]
        }
        const distance = Math.min(distanceLoop[k], maxDistance);
        const scoreBoosts = getBoostingForProfiles(user, maxDistance, params);
        const start = new Date().getTime();
        allProfiles = allProfiles.concat(
          await getLocalProfilesFn(
            user,
            allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
            numProfiles - allProfiles.length,
            params,
            distance,
            scoreBoosts,
          ),
        );
        const end = new Date().getTime();
        console.log(`User ${user._id} Time to get local profiles with distance ${distance} is: ${end-start} ms`);
        if (distance >= maxDistance) {
          break;
        }
      }
    }

    // not interplanetary - cannot expand search
    if (!isInterplanetary) {
      return allProfiles;
    }

    // interplanetary
    const start = new Date().getTime();
    if (params.embeddingAifilter && useAtlasSearch(user)) {
      params.vectorSearchResultIds = await getVectorSearchResults(user, params.embeddingAifilter, exclusionList);
    }
    allProfiles = allProfiles.concat(
      await getInterplanetaryProfilesV2(
        user,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        numProfiles - allProfiles.length,
        params,
      ),
    );
    const end = new Date().getTime();
    console.log(`User ${user._id} Time to get global profiles: ${end-start} ms`);

    return allProfiles;
  }
  catch (err) {
    if (err?.message?.includes('BSONObj size')) {
      user.sizeOfExclusionUsedDuringFailure = exclusionList.length || 0;
      user.exclusionListFailed = true;
      await user.save();
      await actionLib.recalculateExclusionList(user._id, true);
    }
    throw err;
  }
}

function removeDuplicateProfiles(profiles) {
  return _.uniq(profiles, false, function(p) { return p._id; });
}

function returnProfilesWithUsersWhoLiked(user, numProfiles, allProfiles, usersWhoLiked, allUsersWhoLikedIds) {
  let finalProfiles = allProfiles;
  if (usersWhoLiked && usersWhoLiked.length) {
    const excess = allProfiles.length + usersWhoLiked.length - numProfiles;
    if (excess > 0) {
      for (let i = 0; i < excess; i++) {
        finalProfiles.pop();
      }
    }
    if (user.currentDayMetrics.showSecretAdmirerOnSwipe > 0) {
      // secret admirer should come before users who liked
      let additionalUsersWhoLiked = [];
      let otherProfiles = [];
      for (const profile of finalProfiles) {
        if (allUsersWhoLikedIds.includes(profile._id)) {
          additionalUsersWhoLiked.push(profile);
        } else {
          otherProfiles.push(profile);
        }
      }
      let part1 = otherProfiles.slice(0, user.currentDayMetrics.showSecretAdmirerOnSwipe);
      let part2 = otherProfiles.slice(user.currentDayMetrics.showSecretAdmirerOnSwipe);
      part2 = part2.concat(usersWhoLiked).concat(additionalUsersWhoLiked);
      part2 = _.shuffle(part2);
      finalProfiles = part1.concat(part2);
    } else {
      finalProfiles = finalProfiles.concat(usersWhoLiked);
      finalProfiles = _.shuffle(finalProfiles);
    }
  }
  finalProfiles = removeDuplicateProfiles(finalProfiles);
  return formatProfiles(finalProfiles, user, { fromDailyProfiles: true } );
}

async function getProfiles(user, numProfiles, sortBest, prefetch, sortBestForNewUserPercent) {
  if (numProfiles == 0) {
    return [];
  }

  // calculate max distance and interplanetary setting
  let isInterplanetary = false;
  let maxDistance = 100;
  if (user.preferences.global !== undefined) {
    isInterplanetary = user.preferences.global;
  } else {
    maxDistance = premiumLib.getPreferences(user).distance;
    if (maxDistance > constants.maxDistanceFilter) {
      isInterplanetary = true;
      maxDistance = constants.maxDistanceFilter;
    }
  }
  if (user.preferences.distance2) {
    maxDistance = user.preferences.distance2;
  }

  // find exclusion list
  let exclusionList = [];
  if (!user.exclusionListFailed) {
    const start = new Date().getTime();
    const exclusionListDoc = await ExclusionList.findOne({ user: user._id });
    const end = new Date().getTime();
    console.log(`User ${user._id} Time to load exclusion list: ${end-start} ms. Exclusion list length: ${exclusionListDoc?.exclusionList?.length}`);

    if (exclusionListDoc) {
      exclusionList = exclusionListDoc.exclusionList;
      if (sortBest) {
        exclusionList = exclusionList.concat(exclusionListDoc.topProfilesExclusionList);
      }
    }
  }
  // exclude current top picks
  if (user.currentDayMetrics.topPicks?.length > 0) {
    exclusionList = exclusionList.concat(user.currentDayMetrics.topPicks);
  }
  // exclude pending swipes
  if (user.currentDayMetrics.pendingSwipes?.length > 0) {
    exclusionList = exclusionList.concat(user.currentDayMetrics.pendingSwipes);
  }

  const params = {
    minAge: user.preferences.minAge,
    maxAge: user.preferences.maxAge,
    sortBest,
    prefetch,
  };

  /*
  if (sortBest && user.minDistanceForCarrotAlgo && user.isConfigTrue('app_161')) {
    params['minDistanceForCarrotAlgo'] = user.minDistanceForCarrotAlgo
  }
  */

  let allUsersWhoLikedIds = [];
  let usersWhoLiked = [];
  if (user.versionAtLeast('1.13.48') && !premiumLib.isPremium(user) && !prefetch) {
    // get users who liked
    const usersWhoLikedDoc = await UsersWhoLiked.findOne({user});
    if (usersWhoLikedDoc) {
      allUsersWhoLikedIds = usersWhoLikedDoc.usersWhoLiked;
      if (user.currentDayMetrics.cachedUsersWhoLikedSaved) {
        if (user.currentDayMetrics.cachedUsersWhoLiked.length) {
          if (useAtlasSearch(user)) {
            let filters = buildMatchFilterAtlasSearch(user, false, params, null, maxDistance);
            filters = filters.concat([
              {
                in: {
                  path: "_id",
                  value: user.currentDayMetrics.cachedUsersWhoLiked,
                }
              },
            ]);
            usersWhoLiked = await executeQueryAtlasSearch(user, filters, user.currentDayMetrics.cachedUsersWhoLiked.length, exclusionList);
          } else {
            let query = buildMatchFilter(user, exclusionList, false, params, null, maxDistance);
            query._id.$in = user.currentDayMetrics.cachedUsersWhoLiked;
            usersWhoLiked = await executeQuery(user, query, user.currentDayMetrics.cachedUsersWhoLiked.length);
          }
        }
      } else {
        const numTotalLikes = usersWhoLikedDoc.usersWhoLiked.length;
        let numUsersToFind = 0;
        if (numTotalLikes > 0) {
          numUsersToFind = DAILY_USER_WHO_LIKED_PROFILE_LIMIT ?  DAILY_USER_WHO_LIKED_PROFILE_LIMIT : 10;
        }
        if (numUsersToFind) {
          //premium user processing for user who liked
          params.userIds = usersWhoLikedDoc.usersWhoLiked;
          usersWhoLiked = usersWhoLiked.concat(
            await getProfilesHelperV3(
              user,
              numUsersToFind - usersWhoLiked.length,
              isInterplanetary,
              maxDistance,
              exclusionList,
              params,
            )
          );
        }
        // cache the results
        if (usersWhoLiked.length) {
          user.currentDayMetrics.cachedUsersWhoLikedSaved = true;
          user.currentDayMetrics.cachedUsersWhoLiked = usersWhoLiked;
          await user.save();
        }
        params.userIds = undefined;
      }
    }
  }

  // get user ai filter preference
  if (user.aiFilterPreference && useAtlasSearch(user)) {
    const userFilter = await AiFilter.findById(user.aiFilterPreference);
    params.embeddingAifilter = userFilter?.filterEmbedding;
    params.refAifilter = user.aiFilterPreference;
  }

  if (usersWhoLiked.length) {
    if (useAtlasSearch(user) && params.embeddingAifilter) {
      const aifilteredResults = await getVectorSearchResults(user, params.embeddingAifilter, null, null, null, usersWhoLiked);
      const aifilteredIds = aifilteredResults.map(x => x.user);
      usersWhoLiked = usersWhoLiked.filter((x) => aifilteredIds.includes(x._id));
    }
    exclusionList = exclusionList.concat(usersWhoLiked.map(x => x._id));
  }

  if (prefetch) {
    exclusionList = exclusionList.concat(user.recentRecommendations);
  }
  else if (user.recentRecommendations && user.recentRecommendations.length > 0
    && !params.sortBest)
  {
    let vectorSearchResultIds;
    if (params.embeddingAifilter) {
      vectorSearchResultIds = await getVectorSearchResults(
        user,
        params.embeddingAifilter,
        exclusionList,
        null,
        null,
        user.recentRecommendations.map(x => ({ _id: x })),
      );
    }
    // check for cached recent recommendations
    if (user.preferences.local !== false && user.location) {
      let allProfiles;
      if (useAtlasSearch(user)) {
        // if user has ai filters enabled, use vector search first
        let filters = buildMatchFilterAtlasSearch(user, false, params, null, maxDistance);
        filters = filters.concat([
          {
            in: {
              path: "_id",
              value: user.recentRecommendations,
            }
          },
        ]);
        allProfiles = await executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, null, vectorSearchResultIds);
      } else {
        const query = buildMatchFilter(user, exclusionList, false, params, null, maxDistance);
        query._id.$in = user.recentRecommendations;
        allProfiles = await executeQuery(user, query, numProfiles);
      }

      if (allProfiles.length) {
        return returnProfilesWithUsersWhoLiked(user, numProfiles, allProfiles, usersWhoLiked, allUsersWhoLikedIds);
      }
    }

    if (isInterplanetary) {
      let allProfiles;
      if (useAtlasSearch(user)) {
        let filters = buildMatchFilterAtlasSearch(user, true, params, null);
        filters = filters.concat([
          {
            in: {
              path: "_id",
              value: user.recentRecommendations,
            }
          },
        ]);
        allProfiles = await executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, null, vectorSearchResultIds);
      } else {
        const query = {
          ...buildMatchFilter(user, exclusionList, true, params, null),
        };
        query._id.$in = user.recentRecommendations;
        allProfiles = await executeQuery(user, query, numProfiles);
      }

      if (allProfiles.length) {
        return returnProfilesWithUsersWhoLiked(user, numProfiles, allProfiles, usersWhoLiked, allUsersWhoLikedIds);
      }
    }

    if (user.preferences.countries && user.preferences.countries.length > 0 && !countryLib.shouldRemoveCountryFilter(user)) {
      let allProfiles;
      if (useAtlasSearch(user)) {
        let filters = buildMatchFilterAtlasSearch(user, true, params, null);
        filters = filters.concat([
          {
            in: {
              path: "countryCode",
              value: user.preferences.countries,
            }
          },
          {
            in: {
              path: "_id",
              value: user.recentRecommendations,
            }
          },
        ]);
        allProfiles = await executeQueryAtlasSearch(user, filters, numProfiles, exclusionList, null, vectorSearchResultIds);
      } else {
        const query = {
          countryCode: { $in: user.preferences.countries },
          ...buildMatchFilter(user, exclusionList, true, params, null),
        };
        query._id.$in = user.recentRecommendations;
        allProfiles = await executeQuery(user, query, numProfiles);
      }

      if (allProfiles.length) {
        return returnProfilesWithUsersWhoLiked(user, numProfiles, allProfiles, usersWhoLiked, allUsersWhoLikedIds);
      }
    }
  }

  let allProfiles = [];
  if (sortBestForNewUserPercent > 0) {
    params.sortBestForNewUser = true;
    allProfiles = await getProfilesHelperV3(
      user,
      Math.floor(sortBestForNewUserPercent * numProfiles),
      isInterplanetary,
      maxDistance,
      exclusionList,
      params,
    );

    params.sortBestForNewUser = false;
    allProfiles = allProfiles.concat(
      await getProfilesHelperV3(
        user,
        numProfiles - allProfiles.length,
        isInterplanetary,
        maxDistance,
        allProfiles.length ? exclusionList.concat(allProfiles.map((x) => x._id)) : exclusionList,
        params,
      ),
    );

    allProfiles = _.shuffle(allProfiles);
  } else {
    allProfiles = await getProfilesHelperV3(
      user,
      numProfiles,
      isInterplanetary,
      maxDistance,
      exclusionList,
      params,
    );
  }
  // If no profiles found and the user has showUsersOutsideMyRange enabled, expand the search distance
  if (allProfiles.length == 0 && user.preferences.showUsersOutsideMyRange && maxDistance < 100 && user.versionAtLeast('1.13.58')) {
    let expandedMaxDistance = maxDistance;
    while (allProfiles.length == 0 && expandedMaxDistance < 100) {
      expandedMaxDistance += 100;  // Increase distance by x miles at a time for now Setting it to 100
      if (expandedMaxDistance > 100) {
        expandedMaxDistance = 100;
      }
      allProfiles = await getProfilesHelperV3(
        user,
        numProfiles,
        isInterplanetary,
        expandedMaxDistance,
        exclusionList,
        params,
      );
    }
  }

  if (allProfiles.length == 0 && !params.sortBest && !prefetch && !params.refAifilter) {
    user.recommendationsExhaustedAt = new Date();
  }
  if (allProfiles.length > 0 && !params.sortBest) {
    let minDistance = Infinity
    for (const profile of allProfiles) {
      const distance = locationLib.getDistanceFromUser(profile, user);
      if (typeof distance === 'number' && distance < minDistance) {
        minDistance = distance;
      }
    }
    if (minDistance != Infinity) {
      user.minDistanceForCarrotAlgo = minDistance
    }
    await User.updateOne(
      { _id: user._id },
      { $addToSet: { recentRecommendations: { $each: allProfiles } } },
    );
    user.recentRecommendationsSavedAt = new Date();
  }

  if (params.sortBest) {
    if (allProfiles.length > 0) {
      user.recentCarrotRecommendations = allProfiles.map(x => x._id);
    }
    if(allProfiles[0]?.metrics?.numActionsReceived >= 30 && allProfiles[0]?.scores?.likeRatioScore >= 18){
      user.metrics.carrotProfilesSeenByCarrotAlgoV2 = user.metrics.carrotProfilesSeenByCarrotAlgoV2 ? user.metrics.carrotProfilesSeenByCarrotAlgoV2 + 1 : 1
    }
    if(allProfiles[0]?.scores?.totalScore2 == 3 || allProfiles[0]?.scores?.totalScore2 == 4){
      user.metrics.carrotProfilesSeenByCarrotAlgoV1 = user.metrics.carrotProfilesSeenByCarrotAlgoV1 ? user.metrics.carrotProfilesSeenByCarrotAlgoV1 + 1 : 1
    }
  }
  await user.save();

  return returnProfilesWithUsersWhoLiked(user, numProfiles, allProfiles, usersWhoLiked, allUsersWhoLikedIds);
}

async function getWebProfilesHelper(filters) {
  let profiles = [];
  let score = 4;
  while (profiles.length < 5 && score >= 0) {
    filters['scores.totalScore2'] = score;
    profiles = profiles.concat(
      await User.find(filters)
                .read(readPreference, replicaTags)
                .limit(5)
                .lean()
    )
    score = score - 1;
  }
  return profiles;
}

function interleaveArrays(array1, array2) {
  var result = [];
  var i, l = Math.min(array1.length, array2.length);

  for (i = 0; i < l; i++) {
    result.push(array1[i], array2[i]);
  }
  result.push(...array1.slice(l), ...array2.slice(l));
  return result;
}

async function getWebProfiles(req) {
  let filters = {}

  const geo = geoip.lookup(req.ip);
  if (geo) {
    const latitude = geo.ll[0];
    const longitude = geo.ll[1];
    const location = {
      coordinates: [longitude, latitude],
    };
    filters = locationLib.getLatLongQuery(location, 50);
  }

  filters['viewableInDailyProfiles'] = true;
  filters['metrics.numPendingReports'] = 0;

  // get male profiles
  filters['gender'] = 'male';
  const maleProfiles = await getWebProfilesHelper(filters);

  // get female profiles
  filters['gender'] = 'female';
  const femaleProfiles = await getWebProfilesHelper(filters);

  const allProfiles = interleaveArrays(maleProfiles, femaleProfiles);
  return formatProfiles(allProfiles);
}

async function getTopPicks(user, fillEmptySlots) {
  // fillEmptySlots is used to find additional top picks after purchasing premium

  let numTopPicks = 8;
  let allProfiles = [];

  // get current day's top picks if available
  if (user.currentDayMetrics.topPicks?.length > 0) {
    const query = {
      _id: { $in: user.currentDayMetrics.topPicks },
      shadowBanned: { $ne: true },
      viewableInDailyProfiles: true,
    };
    allProfiles = await executeQuery(user, query, numTopPicks);
    if (allProfiles.length == numTopPicks || !fillEmptySlots) {
      return formatProfiles(allProfiles, user, { fromTopPickProfiles: true });
    }
  }

  // calculate max distance and interplanetary setting
  let isInterplanetary = false;
  let maxDistance = 100;
  if (user.preferences.global !== undefined) {
    isInterplanetary = user.preferences.global;
  } else {
    maxDistance = premiumLib.getPreferences(user).distance;
    if (maxDistance > constants.maxDistanceFilter) {
      isInterplanetary = true;
      maxDistance = constants.maxDistanceFilter;
    }
  }
  if (user.preferences.distance2) {
    maxDistance = user.preferences.distance2;
  }

  // find exclusion list
  let exclusionList = [];
  if (!user.exclusionListFailed) {
    const exclusionListDoc = await ExclusionList.findOne({ user: user._id });
    if (exclusionListDoc) {
      exclusionList = exclusionListDoc.exclusionList;
    }
  }

  // exclude prior top picks
  // empty update with upsert, to find or create
  let topPicksExclusionList = await TopPicksExclusionList.findOneAndUpdate(
    { user: user._id },
    {},
    { upsert: true  },
  );
  if (topPicksExclusionList) {
    exclusionList = exclusionList.concat(topPicksExclusionList.priorTopPicks);
  }

  // exclude user's recent recommendations
  if (user.recentRecommendations && user.recentRecommendations.length > 0) {
    exclusionList = exclusionList.concat(user.recentRecommendations);
  }

  // exclude user's recent carrot profiles
  if (user.recentCarrotRecommendations && user.recentCarrotRecommendations.length > 0) {
    exclusionList = exclusionList.concat(user.recentCarrotRecommendations);
  }

  const params = {
    minAge: user.preferences.minAge,
    maxAge: user.preferences.maxAge,
    sortBest: true,
  };

  let genderFilters = []
  let isDating = false
  if (user.preferences.dating.length) {
    genderFilters = [...user.preferences.dating]
    isDating = true
  } else if (user.preferences.friends.length && !user.preferences.dating.length) {
    genderFilters = [...user.preferences.friends]
    genderHash = genderPreferenceLib.hashGenderPreference(user.gender, [], user.preferences.friends)
  }
  if (genderFilters.length > 0) {
    let genderLimits = {};
    if (genderFilters.length === 3) {
      genderLimits = { male: 3, female: 3, 'non-binary': 2 }
    } else if (genderFilters.length === 2) {
      genderLimits = { male: 4, female: 4, 'non-binary': 4 }
    } else {
      genderLimits = { male: 8, female: 8, 'non-binary': 8 }
    }
    const profilesByGender = {};
    genderFilters.forEach(gender => {
      profilesByGender[gender] = allProfiles.filter(profile => profile.gender === gender);
    });
    const genderFilteredProfiles = [];
    for (const gender of genderFilters) {
      const existingProfiles = profilesByGender[gender] || [];
      const maxProfiles = genderLimits[gender] || 4;
      if (existingProfiles.length >= maxProfiles) {
        genderFilteredProfiles.push(...existingProfiles.slice(0, maxProfiles));
      } else {
        const limitCount = maxProfiles - existingProfiles.length;
        const additionalProfiles = await getProfilesHelperV3(
          user,
          limitCount,
          isInterplanetary,
          maxDistance,
          exclusionList.concat(existingProfiles.map((x) => x._id)),
          { ...params, genderHashFilter: isDating ? genderPreferenceLib.hashGenderPreference(user.gender, [gender], []) : genderPreferenceLib.hashGenderPreference(user.gender, [], [gender]) }
        );
        genderFilteredProfiles.push(...existingProfiles, ...additionalProfiles);
      }
    }
    allProfiles = [...genderFilteredProfiles];
  }

  allProfiles = allProfiles.concat(
    await getProfilesHelperV3(
      user,
      numTopPicks - allProfiles.length,
      isInterplanetary,
      maxDistance,
      exclusionList.concat(allProfiles.map((x) => x._id)),
      params,
    )
  );

  // save top picks
  user.currentDayMetrics.topPicks = allProfiles;
  await user.save();

  if (allProfiles.length > 0) {
    await TopPicksExclusionList.updateOne(
      { user: user._id },
      { $addToSet: { priorTopPicks: { $each: allProfiles } } },
    );
  }

  return formatProfiles(allProfiles, user, { fromTopPickProfiles: true });
}

function getProfileTags(user, profile){
  // loop profile to get profiles tag
  let tags = []

  //query user data
  const otherUser = profile

  const lastSeen = new Date(otherUser.metrics.lastSeen); // Convert lastSeen to a Date object
  const createdAt = new Date(otherUser.createdAt)
  const now = new Date(); // Get current time
  const thirtyMinutes = 30 * 60 * 1000; // 30 minutes in milliseconds
  const sevenDays = 7 * 24 * 60 * 60 * 1000;

  if (user) {
    // Check if lastSeen is within the last 30 minutes
    if(now - lastSeen <= thirtyMinutes) {
      tags.push('Active Now')
    }

    // check MutualInterests
    const commonInterests = Array.isArray(user.interestNames) && Array.isArray(otherUser.interestNames) ? user.interestNames.filter(interest => otherUser.interestNames.includes(interest)): [];
    if(commonInterests.length > 0){
      tags.push(`Mutual Interests`);
    }

    // check Nearby (within 1 km)
    const otherUserCoordinates = Array.isArray(otherUser?.coordinates)
      ? otherUser.coordinates
      : Array.isArray(otherUser?.location?.coordinates)
        ? otherUser.location.coordinates
        : null;

    if (user.gender != 'female' && Array.isArray(user?.location?.coordinates) && otherUserCoordinates) {
      const nearby = locationLib.isInsideRadius(user.location.coordinates, otherUserCoordinates, 1);
      if (nearby) {
        tags.push('Nearby');
      }
    }

    // check CompatiblePersonality
    const personalityRecommendations = personalityLib.getThreeTierPersonalityRecommendations(user)
    if (Array.isArray(personalityRecommendations?.recommended) && otherUser?.personality?.mbti) {
      if (personalityRecommendations.recommended.includes(otherUser.personality.mbti)) {
        tags.push('Compatible Personality');
      }
    }


    // Check if user sign up in last seven days
    if (now - createdAt <= sevenDays) {
      tags.push('New Soul');
    }

    // check TopSoul
    if (otherUser?.scores?.likeRatioScore >= 18 && otherUser?.metrics?.numActionsReceived >= 30) {
      tags.push('Top Soul');
    }
  }

  // console.log(`user ${profile._id} tags : ${tags}`)
  return tags
}

function getMetricTags(tags, isLike = false){
  let metrics = []

  if(tags.includes('Active Now')){
    const metricsTag = isLike ? ['numActionsSentActiveNow','numLikesSentActiveNow'] : ['numActionsSentActiveNow']
    metrics.push(...metricsTag)
  }

  if(tags.includes('New Soul')){
    const metricsTag = isLike ? ['numActionsSentNewSoul','numLikesSentNewSoul'] : ['numActionsSentNewSoul']
    metrics.push(...metricsTag)
  }

  if (tags.includes('Mutual Interests')) {
    const metricsTag = isLike ? ['numActionsSentMutualInterests', 'numLikesSentMutualInterests'] : ['numActionsSentMutualInterests']
    metrics.push(...metricsTag);
  }

  if(tags.includes('Nearby')){
    const metricsTag = isLike ? ['numActionsSentNearby','numLikesSentNearby'] : ['numActionsSentNearby']
    metrics.push(...metricsTag)
  }

  if(tags.includes('Top Soul')){
    const metricsTag = isLike ? ['numActionsSentTopSoul','numLikesSentTopSoul'] : ['numActionsSentTopSoul']
    metrics.push(...metricsTag)
  }

  if(tags.includes('Compatible Personality')){
    const metricsTag = isLike ? ['numActionsSentCompatiblePersonality','numLikesSentCompatiblePersonality'] : ['numActionsSentCompatiblePersonality']
    metrics.push(...metricsTag)
  }

  if(metrics.length === 0){
    const metricsTag = isLike ? ['numActionsSentNoTags','numLikesSentNoTags'] : ['numActionsSentNoTags']
    metrics.push(...metricsTag)
  }

  return metrics
}

/*

Fetch Profiles Loops.

MainLoop (Max 5) 12+12+12+6+24=66

1.if userPref.local!= false
  .Start ActiveFirstLoop with distance 50
  .Start ActiveFirstLoop with distance 80

if userPref.global ==true & userPref.local ==true & userPref.distance <= MAX DISTANCE
  .Start ActiveFirstLoop with distance MAX DISTANCE
  .Start ActiveOnlyLoop in user's country (if exists)

if userPref.global true || userPref.distance > MAX DISTANCE
  .Start InterPlanetary Profiles Loop

InterPlanetary Profiles Loop(Max 4 Loops) 6x4=24
  if user hasPreferredCountries
    .Start ActiveFirstLoop within user preferred countries (if exists)
  else
    .Start ActiveOnlyLoop within same tier
    .Start ActiveOnlyLoop within different tier

    //the following are to be skipped if sortBest is true (as excludeInactive becomes false in the following scoreLoop)
    .Start InactiveOnlyLoop within same tier
    .Start InactiveOnlyLoop within different tier

ActiveFirstLoop(MAX 2 Loops) 6+6=12
  .Start ActiveOnly Loop

  //the following are to be skipped if sortBest is true (as excludeInactive becomes false in the following scoreLoop)
  .Start InactiveOnly Loop

ActiveOnlyLoop (1 Loop) 6
  .Start ScoreLoop with exludeInactive true

InactiveOnlyLoop (1 Loop) 6
  .Start ScoreLoop with exludeInactive false

ScoreLoop(Max 2 Loops)  2x3=6
  if sortBest is true
    .Start AgeLoop for score (MIN SCORE to MAX SCORE) And Set excludeActive to false to fetch non-Active profiles too
  else
    .Start AgeLoop ( userScore-10 to userScore+10 )
    .Start AgeLoop(MIN SCORE to userScore-10 & userScore+10 to MAX SCORE)[all scores scanned earlier are to be excluded]

AgeLoop(Max 3 Loops)
  if userAge not in Range [userPrefMinAge-userPrefMaxAge]
    .Start Profiles Loop without any age filter
  else

    //The Following Loops Are to be constrained within userPrefMinAge and userPrefMaxAge (if the ranges upperthreshold goes upto 50, the threshold is macthed to the value userPrefMaxAge)
    .Profiles Loop with age (userAge-5 to userAge+5)
    .Profiles Loop with age (userAge-10 to userAge-5 & userAge+5 to userAge+5)[exclude earlier scanned ranges]
    .Profiles Loop with age (userAge-15 to userAge-10 & userAge+10 to userAge+15)[exclude earlier scanned ranges]
*/
module.exports = {
  getProfiles,
  getWebProfiles,
  countNumLocalUsersTargetGender,
  getTopPicks,
  removeDuplicateProfiles,
  getProfileTags,
  getMetricTags,
  getVectorSearchResults,
};
